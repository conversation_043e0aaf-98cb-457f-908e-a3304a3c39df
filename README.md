# MENA Sales Dashboard

A comprehensive platform for managing sales data, team performance, and client relationships in the MENA region.

## Project Overview

MENA Sales Dashboard provides a centralized solution for tracking sales metrics, managing team members, and generating professional reports for the Middle East and North Africa market.

## Getting Started

There are several ways to work with this codebase:

### Local Development

To work locally using your preferred IDE:

```sh
# Step 1: Clone the repository
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory
cd mena-sales-dashboard

# Step 3: Install the necessary dependencies
npm install

# Step 4: Start the development server
npm run dev
```

### Edit on GitHub

- Navigate to the desired file(s)
- Click the "Edit" button (pencil icon)
- Make your changes and commit

### Use GitHub Codespaces

- Navigate to the main page of the repository
- Click on the "Code" button (green button)
- Select the "Codespaces" tab
- Click on "New codespace"

## Technology Stack

This project is built with:

- Vite - Fast build tool and development server
- TypeScript - Type-safe JavaScript
- React - UI component library
- shadcn-ui - Component library built on Radix UI
- Tailwind CSS - Utility-first CSS framework
- Supabase - Backend as a service for database and authentication

## Deployment

This project is configured for deployment on Cloudflare Pages:

1. Push your changes to the GitHub repository
2. Cloudflare Pages will automatically build and deploy your site
3. The build process uses Node.js 18 and npm

## Project Structure

- `/src` - Source code
  - `/components` - Reusable UI components
  - `/pages` - Page components
  - `/utils` - Utility functions
  - `/hooks` - Custom React hooks
  - `/integrations` - Third-party service integrations
  - `/types` - TypeScript type definitions
- `/public` - Static assets
- `/dist` - Build output (generated)

## Environment Variables

Create a `.env` file with the following variables:

```
VITE_SUPABASE_URL=your-supabase-url
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key
```

## Features

- **Sales Analytics**: Real-time dashboards showing key performance indicators
- **Team Management**: Track employee performance and territory assignments
- **Client Database**: Comprehensive client information and interaction history
- **Proposal Generation**: Create and export professional sales proposals
- **Calendar Integration**: Schedule and manage client meetings and follow-ups
- **Mobile Responsive**: Access your sales data on any device

## Contributing

1. Create a new branch for your feature
2. Make your changes
3. Submit a pull request
4. Ensure all tests pass before merging

## License

This project is proprietary software.
