import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { useState, useEffect } from "react";
import Layout from "../components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Mail, Phone, Briefcase, Loader2 } from "lucide-react";
import { employeesService } from "@/lib/services/employees";

const EmployeeDetail = () => {
  const { id } = useParams<{ id: string }>();
  const [employee, setEmployee] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchEmployee = async () => {
      if (!id) return;

      try {
        setIsLoading(true);
        const data = await employeesService.getById(id);
        if (data) {
          setEmployee(data);
        } else {
          setError("Employee not found");
        }
      } catch (err) {
        console.error("Error fetching employee:", err);
        setError("Failed to fetch employee");
      } finally {
        setIsLoading(false);
      }
    };

    fetchEmployee();
  }, [id]);

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </Layout>
    );
  }

  if (error || !employee) {
    return (
      <Layout>
        <div className="text-center py-10">
          <h1 className="text-2xl font-bold">Employee not found</h1>
          <p className="text-muted-foreground mt-2">
            {error || "The employee you are looking for does not exist."}
          </p>
          <Link
            to="/employees"
            className="mt-4 inline-block text-primary hover:underline"
          >
            Back to Employees
          </Link>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <div className="flex items-center gap-4">
              <Avatar className="h-20 w-20">
                <AvatarImage src={employee.imageUrl} alt={employee.name} />
                <AvatarFallback>
                  {employee.name
                    .split(" ")
                    .map((n: string) => n[0])
                    .join("")}
                </AvatarFallback>
              </Avatar>
              <div>
                <h1 className="text-3xl font-bold">{employee.name}</h1>
                <p className="text-muted-foreground flex items-center gap-2 mt-1">
                  <Briefcase className="h-4 w-4" />
                  {employee.role || "No role specified"}
                </p>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid gap-2 md:grid-cols-2">
              <a
                href={`mailto:${employee.email}`}
                className="flex items-center gap-2 text-sm text-foreground hover:underline"
              >
                <Mail className="h-4 w-4 text-muted-foreground" />
                {employee.email}
              </a>
              {employee.phone && (
                <a
                  href={`tel:${employee.phone}`}
                  className="flex items-center gap-2 text-sm text-foreground hover:underline"
                >
                  <Phone className="h-4 w-4 text-muted-foreground" />
                  {employee.phone}
                </a>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default EmployeeDetail;
