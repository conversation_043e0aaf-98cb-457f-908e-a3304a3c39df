import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Layout from "../components/Layout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import {
  Plus,
  Edit,
  Trash2,
  Download,
  Search,
  FileText,
  Calendar,
  DollarSign,
  Users,
  Loader2,
  ChevronDown,
} from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Proposal, ProposalStatus } from "../types/proposal";
import { useToast } from "@/hooks/use-toast";
import { Skeleton } from "@/components/ui/skeleton";
import { proposalsService } from "@/lib/services";

const ProposalsList = () => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [proposals, setProposals] = useState<Proposal[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadProposals();
  }, []);

  const loadProposals = async () => {
    setLoading(true);
    try {
      const proposalsData = await proposalsService.getAll();
      // Transform the data to match the Proposal interface
      const formattedProposals = proposalsData.map((p) => ({
        id: p.id,
        clientName: p.details?.client_name || "N/A",
        clientEmail: p.details?.client_email || "N/A",
        event: {
          eventName: p.eventName || p.details?.event_name || "N/A",
          eventDate: p.details?.event_date || "N/A",
          eventLocation: p.details?.event_location || "N/A",
          eventType: p.details?.event_type || "N/A",
          audience: p.details?.audience_size || "N/A",
          budget: p.details?.budget_range || "N/A",
          description: p.details?.event_description || "N/A",
        },
        speakers: p.details?.speakers || [],
        totalBudget: p.details?.total_budget || 0,
        createdAt: p.createdAt?.toISOString() || new Date().toISOString(),
        pdfPath: p.pdfPath,
        status: p.status || "Draft",
      })) as Proposal[];
      setProposals(formattedProposals);
    } catch (error) {
      console.error("Error fetching proposals:", error);
      toast({
        title: "Error",
        description: "Could not fetch proposals.",
        variant: "destructive",
      });
      setProposals([]);
    }
    setLoading(false);
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this proposal?")) {
      try {
        await proposalsService.delete(id);
        toast({
          title: "Proposal Deleted",
          description: "The proposal has been successfully deleted.",
        });
        loadProposals();
      } catch (error) {
        toast({
          title: "Error Deleting",
          description: "Could not delete proposal.",
          variant: "destructive",
        });
      }
    }
  };

  const handleDownload = async (proposal: Proposal) => {
    if (!proposal.pdfPath) {
      toast({
        title: "No PDF Available",
        description: "This proposal does not have a PDF file.",
        variant: "destructive",
      });
      return;
    }

    try {
      // Create a download link for the PDF
      const link = document.createElement("a");
      link.href = proposal.pdfPath;
      link.download = `proposal-${proposal.id}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "PDF Downloaded",
        description: "The proposal PDF has been downloaded.",
      });
    } catch (error) {
      console.error("Download error:", error);
      toast({
        title: "Download Failed",
        description: "There was an error downloading the PDF.",
        variant: "destructive",
      });
    }
  };

  const handleStatusChange = async (id: string, newStatus: ProposalStatus) => {
    try {
      // Update the proposal status
      await proposalsService.update({
        id,
        status: newStatus,
      });

      // Update local state
      setProposals(
        proposals.map((p) => (p.id === id ? { ...p, status: newStatus } : p))
      );

      toast({
        title: "Status Updated",
        description: `Proposal status changed to ${newStatus}`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Could not update proposal status.",
        variant: "destructive",
      });
    }
  };

  const filteredProposals = proposals.filter(
    (proposal) =>
      proposal.event.eventName
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      proposal.clientName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      proposal.clientEmail.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <Layout>
        <div className="space-y-6">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <Skeleton className="h-12 w-12 rounded-full" />
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-[250px]" />
                      <Skeleton className="h-4 w-[200px]" />
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Skeleton className="h-8 w-20" />
                    <Skeleton className="h-8 w-20" />
                    <Skeleton className="h-8 w-20" />
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Skeleton className="h-4 w-[100px]" />
                  <Skeleton className="h-4 w-[100px]" />
                  <Skeleton className="h-4 w-[100px]" />
                  <Skeleton className="h-4 w-[100px]" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Proposals</h1>
            <p className="text-muted-foreground mt-2">
              Manage your speaker proposals and track client requests
            </p>
          </div>
          <Button
            onClick={() => navigate("/create-proposal")}
            size="lg"
            className="flex items-center space-x-2"
          >
            <Plus className="h-5 w-5" />
            <span>Create New Proposal</span>
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-blue-800">
                Total Proposals
              </CardTitle>
              <FileText className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-900">
                {proposals.length}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-green-50 to-green-100 border-green-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-green-800">
                Total Value
              </CardTitle>
              <DollarSign className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-900">
                $
                {proposals
                  .reduce((sum, p) => sum + p.totalBudget, 0)
                  .toLocaleString()}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-purple-800">
                Avg. Value
              </CardTitle>
              <DollarSign className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-900">
                $
                {proposals.length > 0
                  ? Math.round(
                      proposals.reduce((sum, p) => sum + p.totalBudget, 0) /
                        proposals.length
                    ).toLocaleString()
                  : "0"}
              </div>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-orange-800">
                Speakers Booked
              </CardTitle>
              <Users className="h-4 w-4 text-orange-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-orange-900">
                {proposals.reduce((sum, p) => sum + p.speakers.length, 0)}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search */}
        <div className="flex items-center space-x-2">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search proposals by client, event, or email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Proposals List */}
        {filteredProposals.length === 0 ? (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12">
              <FileText className="h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold text-foreground mb-2">
                {proposals.length === 0
                  ? "No proposals yet"
                  : "No proposals found"}
              </h3>
              <p className="text-muted-foreground text-center mb-4">
                {proposals.length === 0
                  ? "Get started by creating your first speaker proposal"
                  : "Try adjusting your search criteria"}
              </p>
              {proposals.length === 0 && (
                <Button onClick={() => navigate("/create-proposal")}>
                  <Plus className="h-4 w-4 mr-2" />
                  Create First Proposal
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="grid gap-6">
            {filteredProposals.map((proposal) => (
              <Card
                key={proposal.id}
                className="hover:shadow-md transition-shadow"
              >
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="bg-primary/10 p-3 rounded-full">
                        <FileText className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <CardTitle className="text-xl">
                          {proposal.event.eventName}
                        </CardTitle>
                        <p className="text-muted-foreground">
                          Client: {proposal.clientName} • {proposal.clientEmail}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() =>
                          navigate(`/proposals/edit/${proposal.id}`)
                        }
                      >
                        <Edit className="h-4 w-4 mr-1" />
                        Edit
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleDownload(proposal)}
                      >
                        <Download className="h-4 w-4 mr-1" />
                        PDF
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDelete(proposal.id)}
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Delete
                      </Button>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="outline"
                            size="sm"
                            className="w-[120px]"
                          >
                            <span className="truncate">
                              {proposal.status || "Set Status"}
                            </span>
                            <ChevronDown className="h-4 w-4 ml-2" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuLabel>Change Status</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onSelect={() =>
                              handleStatusChange(proposal.id, "Draft")
                            }
                          >
                            Draft
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onSelect={() =>
                              handleStatusChange(proposal.id, "Pending")
                            }
                          >
                            Pending
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onSelect={() =>
                              handleStatusChange(proposal.id, "Approved")
                            }
                          >
                            Approved
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onSelect={() =>
                              handleStatusChange(proposal.id, "Rejected")
                            }
                          >
                            Rejected
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="flex items-center space-x-2">
                      <Calendar className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {proposal.event.eventDate || "Date TBD"}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Users className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">
                        {proposal.speakers.length} Speaker
                        {proposal.speakers.length !== 1 ? "s" : ""}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm font-semibold text-green-600">
                        ${proposal.totalBudget.toLocaleString()}
                      </span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge variant="secondary" className="text-xs">
                        {new Date(proposal.createdAt).toLocaleDateString()}
                      </Badge>
                    </div>
                  </div>

                  {proposal.event.eventLocation && (
                    <p className="text-sm text-muted-foreground mt-2">
                      📍 {proposal.event.eventLocation}
                    </p>
                  )}

                  <div className="flex flex-wrap gap-2 mt-3">
                    {proposal.speakers
                      .slice(0, 3)
                      .map((speakerProposal, index) => (
                        <Badge
                          key={index}
                          variant="outline"
                          className="text-xs"
                        >
                          {speakerProposal.speaker.name}
                        </Badge>
                      ))}
                    {proposal.speakers.length > 3 && (
                      <Badge variant="outline" className="text-xs">
                        +{proposal.speakers.length - 3} more
                      </Badge>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>
    </Layout>
  );
};

export default ProposalsList;
