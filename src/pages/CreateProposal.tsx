import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import Layout from "@/components/Layout";
import { ProposalHeader } from "@/components/proposals/ProposalHeader";
import { ClientInformation } from "@/components/proposals/ClientInformation";
import { EventDetails } from "@/components/proposals/EventDetails";
import { EmployeeSelection } from "@/components/proposals/EmployeeSelection";
import SpeakerSelection from "@/components/proposals/SpeakerSelection";
import { ProposalActions } from "@/components/proposals/ProposalActions";

import { seedSpeakersIfEmpty } from "@/utils/seedSpeakers";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { CheckCircle2, User, Calendar, Users, FileText } from "lucide-react";
import {
  speakersService,
  employeesService,
  proposalsService,
} from "@/lib/services";

export default function CreateProposal() {
  const navigate = useNavigate();
  const { toast } = useToast();

  const [clientInfo, setClientInfo] = useState({
    name: "",
    email: "",
    company: "",
    phone: "",
    notes: "",
  });

  const [eventInfo, setEventInfo] = useState({
    name: "",
    date: "",
    location: "",
    duration: "",
    audience: "",
    budget: "",
    objectives: "",
    requirements: "",
  });

  const [selectedSpeakers, setSelectedSpeakers] = useState<
    Array<{
      id: string;
      name: string;
      bio?: string;
      category?: string;
      image?: string;
      rate?: number;
      including?: string;
      location?: string;
      experience?: string;
    
      availability?: string;
    }>
  >([]);
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<string>("");

  // Fetch speakers for selection
  const fetchSpeakers = async () => {
    try {
      const speakers = await speakersService.getAll();
      return speakers;
    } catch (error) {
      console.error("Error fetching speakers:", error);
      throw error;
    }
  };

  const { data: speakers = [], isLoading: speakersLoading } = useQuery({
    queryKey: ["speakers", "drizzle"], // Added "drizzle" to force cache refresh
    queryFn: fetchSpeakers,
    staleTime: 0, // Always fetch fresh data
    gcTime: 0, // Don't cache (newer React Query property)
  });

  // Fetch employees for selection
  const fetchEmployees = async () => {
    try {
      const employees = await employeesService.getAll();
      return employees;
    } catch (error) {
      console.error("Error fetching employees:", error);
      throw error;
    }
  };

  const { data: employees = [] } = useQuery({
    queryKey: ["employees", "drizzle"], // Added "drizzle" to force cache refresh
    queryFn: fetchEmployees,
    staleTime: 0, // Always fetch fresh data
    gcTime: 0, // Don't cache (newer React Query property)
  });

  // Seed speakers if database is empty
  useEffect(() => {
    const initializeSpeakers = async () => {
      await seedSpeakersIfEmpty();
    };
    initializeSpeakers();
  }, []);

  const handleBack = () => {
    navigate("/proposals");
  };

  const handleSave = async () => {
    try {
      // Validate required fields
      if (
        !clientInfo.name ||
        !clientInfo.email ||
        !eventInfo.name ||
        !eventInfo.date ||
        !selectedEmployeeId
      ) {
        toast({
          title: "Validation Error",
          description:
            "Please fill in all required fields including sales representative.",
          variant: "destructive",
        });
        return;
      }

      // Get selected employee details
      const selectedEmployee = employees.find(
        (emp) => emp.id === selectedEmployeeId
      );

      // Create proposal
      const proposalData = {
        eventName: eventInfo.name,
        status: "draft",
        details: {
          client_name: clientInfo.name,
          client_email: clientInfo.email,
          client_company: clientInfo.company,
          client_phone: clientInfo.phone,
          client_notes: clientInfo.notes,
          event_date: eventInfo.date,
          event_location: eventInfo.location,
          event_description: eventInfo.objectives,
          audience_size: eventInfo.audience,
          event_type: eventInfo.requirements,
          budget_range: eventInfo.budget,
          speakers: selectedSpeakers.map((s) => s.id), // Store speaker IDs for backward compatibility
          speaker_details: selectedSpeakers, // Store full speaker data with custom images/rates
          employee_id: selectedEmployeeId, // Store selected employee ID
          employee_contact: selectedEmployee
            ? {
                name: selectedEmployee.name,
                email: selectedEmployee.email,
                role: selectedEmployee.role,
                phone: selectedEmployee.phone,
              }
            : null,
          total_budget: selectedSpeakers.reduce(
            (sum, speaker) => sum + (speaker.rate || 0),
            0
          ),
        },
        speakerId: selectedSpeakers[0]?.id || null, // Use first selected speaker as primary
      };

      await proposalsService.create(proposalData);

      toast({
        title: "Success",
        description: "Proposal created successfully!",
      });
      navigate("/proposals");
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create proposal";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  return (
    <Layout>
      <div className="container mx-auto px-6 py-8 space-y-8">
        <ProposalHeader isEditing={false} onBack={handleBack} />

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <div className="space-y-6">
            <ClientInformation
              clientInfo={clientInfo}
              setClientInfo={setClientInfo}
            />

            <EventDetails
              eventDetails={eventInfo}
              setEventDetails={setEventInfo}
            />

            <EmployeeSelection
              selectedEmployeeId={selectedEmployeeId}
              setSelectedEmployeeId={setSelectedEmployeeId}
              employees={employees}
            />
          </div>

          <div className="space-y-6">
            <SpeakerSelection
              selectedSpeakers={selectedSpeakers}
              setSelectedSpeakers={setSelectedSpeakers}
              onNext={() => {}}
              onPrevious={() => {}}
            />

            <ProposalActions
              onSave={handleSave}
              onCancel={handleBack}
              isEditing={false}
            />
          </div>
        </div>
      </div>
    </Layout>
  );
}
