import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { <PERSON>, CardContent, <PERSON>Header, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Plus, Edit, FileText, Download, Eye, Trash2 } from "lucide-react";
import { format } from "date-fns";
import { generateProposalPDF } from "@/utils/pdfGenerator";
import { generateProposalPPTX } from "@/utils/pptxGenerator";
import { Proposal, ProposalStatus } from "@/types/proposal";
import Layout from "@/components/Layout";
import { proposalsService, speakersService } from "@/lib/services";

interface ProposalDetails {
  client_name?: string;
  client_email?: string;
  event_date?: string;
  event_location?: string;
  event_name?: string;
  event_type?: string;
  audience_size?: string;
  budget_range?: string;
  event_description?: string;
  speakers?: string[];
  total_budget?: string;
  speaker_details?: Array<{
    id: string;
    name: string;
    bio?: string;
    category?: string;
    image?: string;
    rate?: number;
    including?: string;
    location?: string;
    experience?: string;

    availability?: string;
    notes?: string;
  }>;
  employee_contact?: {
    name: string;
    email: string;
    phone: string;
  } | null;
}

interface DatabaseProposal {
  id: string;
  eventName?: string;
  status?: string;
  details?: ProposalDetails;
  speakerId?: string;
  createdAt: Date;
  pdfPath?: string;
  submittedDate?: Date;
}

export default function Proposals() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isGenerating, setIsGenerating] = useState(false);

  const fetchProposals = async (): Promise<DatabaseProposal[]> => {
    try {
      const proposals = await proposalsService.getAll();
      return proposals.map((proposal) => ({
        id: proposal.id,
        eventName: proposal.eventName || "",
        status: proposal.status || "",
        details: proposal.details || {},
        speakerId: proposal.speakerId || "",
        createdAt: proposal.createdAt,
        pdfPath: proposal.pdfPath || null,
        submittedDate: proposal.submittedDate || null,
      }));
    } catch (error) {
      console.error("Error fetching proposals:", error);
      throw error;
    }
  };

  const {
    data: proposals,
    isLoading,
    refetch,
  } = useQuery({
    queryKey: ["proposals"],
    queryFn: fetchProposals,
  });

  useEffect(() => {
    refetch();
  }, [refetch]);

  // Transform DatabaseProposal to Proposal interface
  const transformProposal = async (
    dbProposal: DatabaseProposal
  ): Promise<Proposal> => {
    const details = dbProposal.details || {};

    // Fetch speaker details if we have speaker IDs
    let speakers = [];
    if (
      details.speakers &&
      Array.isArray(details.speakers) &&
      details.speakers.length > 0
    ) {
      try {
        // Get speakers by IDs using the speakers service
        const speakerPromises = details.speakers.map((speakerId: string) =>
          speakersService.getById(speakerId)
        );
        const speakerData = await Promise.all(speakerPromises);
        const validSpeakers = speakerData.filter((speaker) => speaker !== null);

        // Check if we have speaker_details with custom data (including uploaded images)
        const speakerDetails = details.speaker_details || [];

        console.log(
          "Transform Proposal - Raw speaker_details:",
          speakerDetails
        );
        console.log("Transform Proposal - Speaker IDs:", details.speakers);

        // If we have speaker_details with custom data, use them directly
        if (speakerDetails.length > 0) {
          console.log("Transform Proposal - Using speaker_details directly");
          speakers = speakerDetails.map((customSpeaker) => {
            console.log(
              "Transform Proposal - Processing custom speaker:",
              customSpeaker.name
            );
            console.log(
              "Transform Proposal - Custom speaker image:",
              customSpeaker.image
            );

            return {
              speaker: {
                id: customSpeaker.id,
                name: customSpeaker.name,
                bio: customSpeaker.bio || "",
                category: customSpeaker.category || "",
                image:
                  customSpeaker.image ||
                  `https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face`,
                rate: customSpeaker.rate || 0,
                including: customSpeaker.including || "",
                location: customSpeaker.location || "",
                experience: customSpeaker.experience || "",

                availability: customSpeaker.availability || "Available",
              },
              role: "Speaker",
              notes: customSpeaker.notes || "",
            };
          });
        } else {
          // Fallback to using base speaker data
          console.log(
            "Transform Proposal - Using base speaker data (no custom details)"
          );
          speakers = validSpeakers.map((speaker) => {
            console.log(
              "Transform Proposal - Processing base speaker:",
              speaker?.name
            );

            return {
              speaker: {
                id: speaker?.id || "",
                name: speaker?.name || "Unknown Speaker",
                bio: speaker?.bio || "",
                category: speaker?.category || "",
                // image:
                //   speaker?.image ||
                //   `https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face`,
                rate: speaker?.rate || 0,
                including: "",
                location: speaker?.location || "",
                experience: speaker?.experience || "",

                availability: speaker?.availability || "Available",
              },
              role: "Speaker",
              notes: "",
            };
          });
        }
      } catch (error) {
        console.error("Error fetching speaker details:", error);
      }
    }

    return {
      id: dbProposal.id,
      clientName: details.client_name || "",
      clientEmail: details.client_email || "",
      event: {
        eventName: dbProposal.eventName || details.event_name || "",
        eventDate: details.event_date || "",
        eventLocation: details.event_location || "",
        eventType: details.event_type || "",
        audience: details.audience_size || "",
        budget: details.budget_range || "",
        description: details.event_description || "",
      },
      speakers: speakers,
      totalBudget: parseFloat(
        details.total_budget || details.budget_range || "0"
      ),
      createdAt: dbProposal.createdAt.toISOString(),
      pdfPath: dbProposal.pdfPath || undefined,
      status: (dbProposal.status as ProposalStatus) || "Draft",
      // Include employee contact information for PPTX generation
      employeeContact: details.employee_contact || null,
    };
  };

  const handleGeneratePDF = async (proposal: DatabaseProposal) => {
    setIsGenerating(true);
    try {
      const transformedProposal = await transformProposal(proposal);
      await generateProposalPDF(transformedProposal, null);
      toast({
        title: "PDF Generated",
        description:
          "Proposal PDF has been generated and downloaded successfully.",
      });
    } catch (error: unknown) {
      console.error("PDF generation error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to generate PDF";
      toast({
        title: "Error generating PDF",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleGeneratePPTX = async (proposal: DatabaseProposal) => {
    setIsGenerating(true);
    try {
      const transformedProposal = await transformProposal(proposal);
      await generateProposalPPTX(transformedProposal);
      toast({
        title: "PPTX Generated",
        description:
          "Proposal PPTX has been generated and downloaded successfully.",
      });
    } catch (error: unknown) {
      console.error("PPTX generation error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Failed to generate PPTX";
      toast({
        title: "Error generating PPTX",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (window.confirm("Are you sure you want to delete this proposal?")) {
      try {
        // Note: File storage handling would need to be implemented separately
        // For now, we'll just delete the database record
        await proposalsService.delete(id);

        toast({
          title: "Proposal Deleted",
          description: "The proposal has been successfully deleted.",
        });
        // Refetch proposals to update the UI
        refetch();
      } catch (error) {
        console.error("Delete error:", error);
        toast({
          title: "Error Deleting",
          description:
            "An unexpected error occurred while deleting the proposal.",
          variant: "destructive",
        });
      }
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex justify-center items-center h-64">
          <div className="text-lg">Loading proposals...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container mx-auto px-6 py-8">
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Proposals</h1>
            <p className="text-muted-foreground mt-2">
              Manage and track your speaker proposals
            </p>
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={() => navigate("/edit-template")}
            >
              <Edit className="w-4 h-4 mr-2" />
              Edit Template
            </Button>
            <Button onClick={() => navigate("/create-proposal")}>
              <Plus className="w-4 h-4 mr-2" />
              Create New Proposal
            </Button>
          </div>
        </div>

        {proposals?.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="w-16 h-16 mx-auto text-muted-foreground mb-4" />
            <h2 className="text-2xl font-semibold mb-2">No proposals yet</h2>
            <p className="text-muted-foreground mb-4">
              Create your first proposal to get started
            </p>
            <Button onClick={() => navigate("/create-proposal")}>
              <Plus className="w-4 h-4 mr-2" />
              Create New Proposal
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {proposals?.map((proposal) => {
              const details = proposal.details as ProposalDetails;

              let statusColor:
                | "default"
                | "secondary"
                | "destructive"
                | "outline" = "secondary";
              switch (proposal.status) {
                case "draft":
                  statusColor = "secondary";
                  break;
                case "submitted":
                  statusColor = "default";
                  break;
                case "accepted":
                  statusColor = "default";
                  break;
                case "rejected":
                  statusColor = "destructive";
                  break;
                default:
                  statusColor = "secondary";
                  break;
              }

              return (
                <Card
                  key={proposal.id}
                  className="hover:shadow-lg transition-shadow"
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <CardTitle className="text-lg font-semibold line-clamp-1">
                          {proposal.eventName}
                        </CardTitle>
                        <p className="text-sm text-muted-foreground mt-1">
                          Client: {details?.client_name || "N/A"}
                        </p>
                      </div>
                      <Badge variant={statusColor}>{proposal.status}</Badge>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-4">
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">
                          Event Date:
                        </span>
                        <span>{details?.event_date || "TBD"}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Location:</span>
                        <span className="truncate ml-2">
                          {details?.event_location || "TBD"}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Created:</span>
                        <span>
                          {format(new Date(proposal.createdAt), "MMM dd, yyyy")}
                        </span>
                      </div>
                    </div>

                    <div className="pt-2 border-t">
                      <div className="flex justify-between items-center mb-3">
                        <span className="text-sm font-medium">
                          Speakers: {details?.speakers?.length || 0}
                        </span>
                        <span className="text-sm text-muted-foreground">
                          Budget: {details?.total_budget || "TBD"}
                        </span>
                      </div>

                      <div className="flex gap-2 mb-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() =>
                            navigate(`/proposals/${proposal.id}/edit`)
                          }
                          className="flex-1"
                        >
                          <Edit className="w-3 h-3 mr-1" />
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDelete(proposal.id)}
                          className="flex-1"
                        >
                          <Trash2 className="w-3 h-3 mr-1" />
                          Delete
                        </Button>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleGeneratePDF(proposal)}
                          disabled={isGenerating}
                          className="flex-1"
                        >
                          <FileText className="w-3 h-3 mr-1" />
                          PDF
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleGeneratePPTX(proposal)}
                          disabled={isGenerating}
                          className="flex-1"
                        >
                          <FileText className="w-3 h-3 mr-1" />
                          PPTX
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>
    </Layout>
  );
}
