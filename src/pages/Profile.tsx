import { useUser } from "@clerk/clerk-react";
import Layout from "@/components/Layout";
import {
  Card,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useState } from "react";
import { Loader2 } from "lucide-react";

const Profile = () => {
  const { user } = useUser();
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);

  const getInitials = () => {
    if (user?.fullName) {
      return user.fullName
        .split(" ")
        .map((n) => n[0])
        .join("")
        .slice(0, 2);
    }
    return "ME";
  };

  const handleUploadAvatar = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    try {
      setIsUploading(true);
      if (!event.target.files || event.target.files.length === 0) {
        throw new Error("You must select an image to upload.");
      }

      // Simulate upload delay
      setTimeout(() => {
        toast({
          title: "Avatar updated!",
          description: "Your new avatar has been saved.",
        });
        setIsUploading(false);
      }, 1000);
    } catch (error: unknown) {
      const errorMessage =
        error instanceof Error ? error.message : "An unexpected error occurred";
      toast({
        title: "Error uploading avatar",
        description: errorMessage,
        variant: "destructive",
      });
      setIsUploading(false);
    }
  };

  if (!user) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        <h1 className="text-3xl font-bold tracking-tight text-foreground">
          Profile
        </h1>
        <div className="max-w-xl mx-auto">
          <Card>
            <CardHeader>
              <CardTitle>Your Details</CardTitle>
              <CardDescription>
                Update your personal information and avatar.
              </CardDescription>
            </CardHeader>
            <CardContent className="flex flex-col items-center space-y-4 pt-6">
              <Avatar className="h-24 w-24">
                <AvatarImage src={user.imageUrl} alt="User Avatar" />
                <AvatarFallback className="text-4xl">
                  {getInitials()}
                </AvatarFallback>
              </Avatar>
              <div className="text-center">
                <p className="text-lg font-semibold">
                  {user.primaryEmailAddress?.emailAddress}
                </p>
                <p className="text-sm text-muted-foreground">
                  Joined on {new Date(user.createdAt).toLocaleDateString()}
                </p>
              </div>
              <div className="w-full pt-2">
                <Button
                  asChild
                  className="w-full cursor-pointer"
                  variant="outline"
                  disabled={isUploading}
                >
                  <label htmlFor="avatar-upload">
                    {isUploading ? (
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    ) : null}
                    {isUploading ? "Uploading..." : "Upload Avatar"}
                  </label>
                </Button>
                <Input
                  id="avatar-upload"
                  type="file"
                  className="hidden"
                  accept="image/*"
                  onChange={handleUploadAvatar}
                  disabled={isUploading}
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default Profile;
