import { useState, useEffect } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { db } from "@/db";
import { speakers } from "@/db/schema";
import { eq } from "drizzle-orm";
import Layout from "@/components/Layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import {
  ArrowLeft,
  Edit,
  Save,
  X,
  MapPin,
  User,
  Briefcase,
} from "lucide-react";
import { format } from "date-fns";

const SpeakerDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isEditing, setIsEditing] = useState(false);

  const [formData, setFormData] = useState({
    name: "",
    bio: "",
    category: "",
    location: "",
    experience: "",
    rate: "",
    availability: "",
  });

  const { data: speaker, isLoading } = useQuery({
    queryKey: ["speaker", id],
    queryFn: async () => {
      const result = await db
        .select()
        .from(speakers)
        .where(eq(speakers.id, id))
        .limit(1);
      if (result.length === 0) throw new Error("Speaker not found");
      return result[0];
    },
    enabled: !!id,
  });

  // Update form data when speaker data is loaded
  useEffect(() => {
    if (speaker) {
      setFormData({
        name: speaker.name || "",
        bio: speaker.bio || "",
        category: speaker.category || "",
        location: speaker.location || "",
        experience: speaker.experience || "",
        rate: speaker.rate?.toString() || "",
        availability: speaker.availability || "",
      });
    }
  }, [speaker]);

  const updateSpeakerMutation = useMutation({
    mutationFn: async (updatedData: Partial<typeof speakers.$inferInsert>) => {
      const result = await db
        .update(speakers)
        .set({
          ...updatedData,
          rate: updatedData.rate ? parseInt(updatedData.rate.toString()) : null,
        })
        .where(eq(speakers.id, id))
        .returning();

      if (result.length === 0) throw new Error("Speaker not found");
      return result[0];
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "Speaker updated successfully!",
      });
      queryClient.invalidateQueries({ queryKey: ["speaker", id] });
      queryClient.invalidateQueries({ queryKey: ["speakers"] });
      setIsEditing(false);
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const handleSave = () => {
    const transformedData = {
      ...formData,
      rate: formData.rate ? parseInt(formData.rate) : null,
      availability: formData.availability as
        | "Available"
        | "Busy"
        | "Unavailable",
    };

    updateSpeakerMutation.mutate(transformedData);
  };

  const handleCancel = () => {
    if (speaker) {
      setFormData({
        name: speaker.name || "",
        bio: speaker.bio || "",
        category: speaker.category || "",
        location: speaker.location || "",
        experience: speaker.experience || "",
        rate: speaker.rate?.toString() || "",
        availability: speaker.availability || "",
      });
    }
    setIsEditing(false);
  };

  const getAvailabilityColor = (availability: string | null) => {
    switch (availability) {
      case "Available":
        return "bg-green-100 text-green-800 border-green-200";
      case "Busy":
        return "bg-yellow-100 text-yellow-800 border-yellow-200";
      case "Unavailable":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  if (isLoading) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <User className="h-12 w-12 animate-pulse text-muted-foreground mx-auto mb-4" />
            <p>Loading speaker details...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!speaker) {
    return (
      <Layout>
        <div className="text-center py-12">
          <h2 className="text-2xl font-bold mb-4">Speaker not found</h2>
          <Button onClick={() => navigate("/speakers")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Speakers
          </Button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <Button variant="outline" onClick={() => navigate("/speakers")}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Speakers
          </Button>

          <div className="flex space-x-2">
            {isEditing ? (
              <>
                <Button variant="outline" onClick={handleCancel}>
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  onClick={handleSave}
                  disabled={updateSpeakerMutation.isPending}
                >
                  <Save className="h-4 w-4 mr-2" />
                  Save Changes
                </Button>
              </>
            ) : (
              <Button onClick={() => setIsEditing(true)}>
                <Edit className="h-4 w-4 mr-2" />
                Edit Speaker
              </Button>
            )}
          </div>
        </div>

        {/* Speaker Profile */}
        <Card>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-20 h-20 rounded-full bg-muted flex items-center justify-center border-4 border-background shadow-md">
                  <User className="w-10 h-10 text-muted-foreground" />
                </div>
                <div>
                  {isEditing ? (
                    <div className="space-y-2">
                      <Input
                        value={formData.name}
                        onChange={(e) =>
                          setFormData({ ...formData, name: e.target.value })
                        }
                        className="text-2xl font-bold"
                        placeholder="Speaker name"
                      />
                    </div>
                  ) : (
                    <div>
                      <h1 className="text-3xl font-bold text-foreground">
                        {speaker.name}
                      </h1>
                    </div>
                  )}
                </div>
              </div>

              <div className="text-right">
                {isEditing ? (
                  <Select
                    value={formData.availability}
                    onValueChange={(value) =>
                      setFormData({ ...formData, availability: value })
                    }
                  >
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Availability" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Available">Available</SelectItem>
                      <SelectItem value="Busy">Busy</SelectItem>
                      <SelectItem value="Unavailable">Unavailable</SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  speaker.availability && (
                    <Badge
                      variant="outline"
                      className={getAvailabilityColor(speaker.availability)}
                    >
                      {speaker.availability}
                    </Badge>
                  )
                )}
              </div>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Bio */}
            <div>
              <Label htmlFor="bio" className="text-base font-semibold">
                Biography
              </Label>
              {isEditing ? (
                <Textarea
                  id="bio"
                  value={formData.bio}
                  onChange={(e) =>
                    setFormData({ ...formData, bio: e.target.value })
                  }
                  rows={4}
                  className="mt-2"
                  placeholder="Speaker biography"
                />
              ) : (
                <p className="text-muted-foreground mt-2 leading-relaxed">
                  {speaker.bio || "No biography available"}
                </p>
              )}
            </div>

            {/* Details Grid */}
            <div className="grid gap-6 md:grid-cols-2">
              {/* Category */}
              <div>
                <Label className="text-base font-semibold flex items-center">
                  <Briefcase className="h-4 w-4 mr-2" />
                  Category
                </Label>
                {isEditing ? (
                  <Input
                    value={formData.category}
                    onChange={(e) =>
                      setFormData({ ...formData, category: e.target.value })
                    }
                    className="mt-2"
                    placeholder="Speaker category"
                  />
                ) : (
                  <p className="text-muted-foreground mt-2">
                    {speaker.category || "Not specified"}
                  </p>
                )}
              </div>

              {/* Location */}
              <div>
                <Label className="text-base font-semibold flex items-center">
                  <MapPin className="h-4 w-4 mr-2" />
                  Location
                </Label>
                {isEditing ? (
                  <Input
                    value={formData.location}
                    onChange={(e) =>
                      setFormData({ ...formData, location: e.target.value })
                    }
                    className="mt-2"
                    placeholder="Location"
                  />
                ) : (
                  <p className="text-muted-foreground mt-2">
                    {speaker.location || "Not specified"}
                  </p>
                )}
              </div>

              {/* Rate */}
              <div>
                <Label className="text-base font-semibold">Speaking Rate</Label>
                {isEditing ? (
                  <Input
                    type="number"
                    value={formData.rate}
                    onChange={(e) =>
                      setFormData({ ...formData, rate: e.target.value })
                    }
                    className="mt-2"
                    placeholder="Rate in USD"
                  />
                ) : (
                  <p className="text-muted-foreground mt-2">
                    {speaker.rate
                      ? `$${speaker.rate.toLocaleString()}`
                      : "Not specified"}
                  </p>
                )}
              </div>

              {/* Experience */}
              <div>
                <Label className="text-base font-semibold">Experience</Label>
                {isEditing ? (
                  <Input
                    value={formData.experience}
                    onChange={(e) =>
                      setFormData({ ...formData, experience: e.target.value })
                    }
                    className="mt-2"
                    placeholder="Years of experience"
                  />
                ) : (
                  <p className="text-muted-foreground mt-2">
                    {speaker.experience || "Not specified"}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </Layout>
  );
};

export default SpeakerDetail;
