import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import Layout from "@/components/Layout";
import { ProposalHeader } from "@/components/proposals/ProposalHeader";
import { ClientInformation } from "@/components/proposals/ClientInformation";
import { EventDetails } from "@/components/proposals/EventDetails";
import { SimpleSpeakerSelection } from "@/components/proposals/SimpleSpeakerSelection";
import { ProposalActions } from "@/components/proposals/ProposalActions";

import { proposalsService, speakersService } from "@/lib/services";

interface DatabaseProposal {
  id: string;
  user_id: string;
  event_name: string;
  status: string;
  details: any;
  speaker_id: string;
  created_at: string;
  updated_at: string;
  pdf_path: string | null;
  submitted_date: string | null;
}

export default function EditProposal() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [clientInfo, setClientInfo] = useState({
    name: "",
    email: "",
    company: "",
    phone: "",
    notes: "",
  });

  const [eventInfo, setEventInfo] = useState({
    name: "",
    date: "",
    time: "",
    duration: "",
    location: "",
    description: "",
    audience_size: "",
    event_type: "",
    budget_range: "",
    special_requirements: "",
  });

  const [selectedSpeakers, setSelectedSpeakers] = useState<string[]>([]);

  // Fetch proposal data
  const { data: proposal, isLoading: isLoadingProposal } = useQuery({
    queryKey: ["proposal", id],
    queryFn: async () => {
      if (!id) throw new Error("Proposal ID is required");

      try {
        const data = await proposalsService.getById(id);
        if (!data) throw new Error("Proposal not found");

        // Transform to match DatabaseProposal interface
        return {
          id: data.id,
          user_id: "", // Not used in current implementation
          event_name: data.eventName || "",
          status: data.status || "",
          details: data.details || {},
          speaker_id: data.speakerId || "",
          created_at: data.createdAt.toISOString(),
          updated_at: data.createdAt.toISOString(), // Using createdAt as fallback
          pdf_path: data.pdfPath || null,
          submitted_date: data.submittedDate?.toISOString() || null,
        } as DatabaseProposal;
      } catch (error) {
        console.error("Error fetching proposal:", error);
        throw error;
      }
    },
    enabled: !!id,
  });

  // Fetch speakers
  const { data: speakers = [] } = useQuery({
    queryKey: ["speakers"],
    queryFn: async () => {
      try {
        return await speakersService.getAll();
      } catch (error) {
        console.error("Error fetching speakers:", error);
        throw error;
      }
    },
  });

  // Populate form when proposal data is loaded
  useEffect(() => {
    if (proposal) {
      const details = proposal.details || {};

      setClientInfo({
        name: details.client_name || "",
        email: details.client_email || "",
        company: details.client_company || "",
        phone: details.client_phone || "",
        notes: details.client_notes || "",
      });

      setEventInfo({
        name: proposal.event_name || "",
        date: details.event_date || "",
        time: details.event_time || "",
        duration: details.event_duration || "",
        location: details.event_location || "",
        description: details.event_description || "",
        audience_size: details.audience_size || "",
        event_type: details.event_type || "",
        budget_range: details.budget_range || "",
        special_requirements: details.special_requirements || "",
      });

      setSelectedSpeakers(details.speakers || []);
    }
  }, [proposal]);

  // Update proposal mutation
  const updateProposalMutation = useMutation({
    mutationFn: async (updatedData: any) => {
      if (!id) throw new Error("Proposal ID is required");

      const proposalData = {
        id,
        eventName: updatedData.event_name,
        status: updatedData.status,
        details: updatedData.details,
        speakerId: updatedData.speaker_id,
      };

      return await proposalsService.update(proposalData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["proposal", id] });
      queryClient.invalidateQueries({ queryKey: ["proposals"] });
      toast({
        title: "Proposal Updated",
        description: "Your proposal has been updated successfully.",
      });
    },
    onError: (error) => {
      console.error("Error updating proposal:", error);
      toast({
        title: "Error",
        description: "Failed to update proposal. Please try again.",
        variant: "destructive",
      });
    },
  });

  const handleBack = () => {
    navigate("/proposals");
  };

  const handleSave = async () => {
    try {
      // Validate required fields
      if (
        !clientInfo.name ||
        !clientInfo.email ||
        !eventInfo.name ||
        !eventInfo.date
      ) {
        toast({
          title: "Validation Error",
          description: "Please fill in all required fields.",
          variant: "destructive",
        });
        return;
      }

      // Update proposal
      const updatedData = {
        event_name: eventInfo.name,
        status: "draft",
        details: {
          client_name: clientInfo.name,
          client_email: clientInfo.email,
          client_company: clientInfo.company,
          client_phone: clientInfo.phone,
          client_notes: clientInfo.notes,
          event_date: eventInfo.date,
          event_time: eventInfo.time,
          event_duration: eventInfo.duration,
          event_location: eventInfo.location,
          event_description: eventInfo.description,
          audience_size: eventInfo.audience_size,
          event_type: eventInfo.event_type,
          budget_range: eventInfo.budget_range,
          special_requirements: eventInfo.special_requirements,
          speakers: selectedSpeakers,
          total_budget: eventInfo.budget_range,
        },
        speaker_id: selectedSpeakers[0] || null,
      };

      await updateProposalMutation.mutateAsync(updatedData);
    } catch (error) {
      console.error("Error saving proposal:", error);
      toast({
        title: "Error",
        description: "Failed to save proposal. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSubmit = async () => {
    try {
      // Validate required fields
      if (
        !clientInfo.name ||
        !clientInfo.email ||
        !eventInfo.name ||
        !eventInfo.date
      ) {
        toast({
          title: "Validation Error",
          description: "Please fill in all required fields.",
          variant: "destructive",
        });
        return;
      }

      // Update proposal with submitted status
      const updatedData = {
        event_name: eventInfo.name,
        status: "submitted",
        details: {
          client_name: clientInfo.name,
          client_email: clientInfo.email,
          client_company: clientInfo.company,
          client_phone: clientInfo.phone,
          client_notes: clientInfo.notes,
          event_date: eventInfo.date,
          event_time: eventInfo.time,
          event_duration: eventInfo.duration,
          event_location: eventInfo.location,
          event_description: eventInfo.description,
          audience_size: eventInfo.audience_size,
          event_type: eventInfo.event_type,
          budget_range: eventInfo.budget_range,
          special_requirements: eventInfo.special_requirements,
          speakers: selectedSpeakers,
          total_budget: eventInfo.budget_range,
        },
        speaker_id: selectedSpeakers[0] || null,
      };

      await updateProposalMutation.mutateAsync(updatedData);

      // Navigate to proposals list
      navigate("/proposals");
    } catch (error) {
      console.error("Error submitting proposal:", error);
      toast({
        title: "Error",
        description: "Failed to submit proposal. Please try again.",
        variant: "destructive",
      });
    }
  };

  if (isLoadingProposal) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading proposal...</p>
          </div>
        </div>
      </Layout>
    );
  }

  if (!proposal) {
    return (
      <Layout>
        <div className="text-center py-10">
          <h1 className="text-2xl font-bold">Proposal not found</h1>
          <p className="text-muted-foreground mt-2">
            The proposal you are looking for does not exist.
          </p>
          <button
            onClick={handleBack}
            className="mt-4 inline-block text-primary hover:underline"
          >
            Back to Proposals
          </button>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="max-w-4xl mx-auto space-y-8">
        <ProposalHeader onBack={handleBack} />

        <ClientInformation
          clientInfo={clientInfo}
          setClientInfo={setClientInfo}
        />

        <EventDetails eventDetails={eventInfo} setEventDetails={setEventInfo} />

        <SimpleSpeakerSelection
          speakers={speakers}
          selectedSpeakers={selectedSpeakers}
          setSelectedSpeakers={setSelectedSpeakers}
        />

        <ProposalActions
          onSave={handleSave}
          onSubmit={handleSubmit}
          isValid={
            !!clientInfo.name &&
            !!clientInfo.email &&
            !!eventInfo.name &&
            !!eventInfo.date
          }
        />
      </div>
    </Layout>
  );
}
