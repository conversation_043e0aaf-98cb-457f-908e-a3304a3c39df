import { useQuery } from "@tanstack/react-query";
import Layout from "@/components/Layout";
import DashboardStatCard from "@/components/DashboardStatCard";
import SpeakerCategory<PERSON>hart from "@/components/SpeakerCategoryChart";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Users, FileText, TrendingUp } from "lucide-react";
import { speakersService, proposalsService } from "@/lib/services";
import { useUser } from "@clerk/clerk-react";
import { Loader2 } from "lucide-react";

const Index = () => {
  const { user, isLoaded } = useUser();

  // Fetch speakers count
  const {
    data: speakersCount = 0,
    error: speakersError,
    isLoading: speakersLoading,
  } = useQuery({
    queryKey: ["speakers-count"],
    queryFn: async () => {
      return await speakersService.getCount();
    },
    retry: false, // Don't retry on database connection errors
    enabled: isLoaded && !!user, // Only run when user is authenticated
  });

  // Fetch proposals count
  const {
    data: proposalsCount = 0,
    error: proposalsError,
    isLoading: proposalsLoading,
  } = useQuery({
    queryKey: ["proposals-count"],
    queryFn: async () => {
      return await proposalsService.getCount();
    },
    retry: false,
    enabled: isLoaded && !!user,
  });

  // Fetch speaker categories for chart
  const { data: categoryData = [] } = useQuery({
    queryKey: ["speaker-categories"],
    queryFn: async () => {
      const allSpeakers = await speakersService.getAll();

      const categoryCounts = allSpeakers.reduce(
        (acc: Record<string, number>, speaker) => {
          const category = speaker.category || "Uncategorized";
          acc[category] = (acc[category] || 0) + 1;
          return acc;
        },
        {}
      );

      return Object.entries(categoryCounts).map(([name, total]) => ({
        name,
        total,
      }));
    },
    retry: false,
    enabled: isLoaded && !!user,
  });

  // Show loading state while Clerk is initializing
  if (!isLoaded) {
    return (
      <Layout>
        <div className="flex items-center justify-center h-64">
          <Loader2 className="h-8 w-8 animate-spin" />
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="space-y-8">
        {/* Header */}
        <div>
          <h1 className="text-4xl font-bold text-foreground mb-2">Dashboard</h1>
          <p className="text-muted-foreground text-lg">
            Welcome back,{" "}
            {user?.firstName || user?.emailAddresses[0]?.emailAddress}
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2">
          <DashboardStatCard
            title="Total Speakers"
            value={speakersCount}
            icon={Users}
            trend={{ value: 12, isPositive: true }}
            className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/30 dark:to-indigo-950/30 border-blue-200 dark:border-blue-800"
          />
          <DashboardStatCard
            title="Proposals"
            value={proposalsCount}
            icon={FileText}
            trend={{ value: 5, isPositive: false }}
            className="bg-gradient-to-br from-orange-50 to-amber-50 dark:from-orange-950/30 dark:to-amber-950/30 border-orange-200 dark:border-orange-800"
          />
        </div>

        {/* Charts Grid */}
        <div className="grid gap-6 lg:grid-cols-1">
          {/* Speaker Categories Chart */}
          <Card className="shadow-sm">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Speaker Categories
              </CardTitle>
            </CardHeader>
            <CardContent>
              <SpeakerCategoryChart data={categoryData} />
            </CardContent>
          </Card>
        </div>
      </div>
    </Layout>
  );
};

export default Index;
