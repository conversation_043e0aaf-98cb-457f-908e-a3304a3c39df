import { db } from "@/db";
import { proposals, speakers } from "@/db/schema";
import { eq, desc, asc, like } from "drizzle-orm";

export interface Proposal {
    id: string;
    eventName?: string;
    speakerId?: string;
    details?: any;
    status?: string;
    pdfPath?: string;
    submittedDate?: Date;
    createdAt: Date;
}

export interface CreateProposalData {
    eventName?: string;
    speakerId?: string;
    details?: any;
    status?: string;
    pdfPath?: string;
    submittedDate?: Date;
}

export interface UpdateProposalData extends Partial<CreateProposalData> {
    id: string;
}

export interface ProposalWithSpeaker extends Proposal {
    speaker?: {
        id: string;
        name: string;
        bio?: string;
        category?: string;
        image?: string;
        rate?: number;
    };
}

export class ProposalsService {
    async getAll(): Promise<Proposal[]> {
        try {
            const result = await db.select().from(proposals).orderBy(desc(proposals.createdAt));
            return result.map(this.mapDbToProposal);
        } catch (error) {
            console.error('Error fetching proposals:', error);
            throw new Error('Failed to fetch proposals');
        }
    }

    async getById(id: string): Promise<Proposal | null> {
        try {
            const result = await db.select().from(proposals).where(eq(proposals.id, id)).limit(1);
            return result.length > 0 ? this.mapDbToProposal(result[0]) : null;
        } catch (error) {
            console.error('Error fetching proposal:', error);
            throw new Error('Failed to fetch proposal');
        }
    }

    async getWithSpeaker(id: string): Promise<ProposalWithSpeaker | null> {
        try {
            const result = await db
                .select({
                    proposal: proposals,
                    speaker: speakers,
                })
                .from(proposals)
                .leftJoin(speakers, eq(proposals.speakerId, speakers.id))
                .where(eq(proposals.id, id))
                .limit(1);

            if (result.length === 0) return null;

            const proposal = this.mapDbToProposal(result[0].proposal);
            const speaker = result[0].speaker ? {
                id: result[0].speaker.id,
                name: result[0].speaker.name,
                bio: result[0].speaker.bio || undefined,
                category: result[0].speaker.category || undefined,
                image: result[0].speaker.image || undefined,
                rate: result[0].speaker.rate || undefined,
            } : undefined;

            return {
                ...proposal,
                speaker,
            };
        } catch (error) {
            console.error('Error fetching proposal with speaker:', error);
            throw new Error('Failed to fetch proposal with speaker');
        }
    }

    async create(data: CreateProposalData): Promise<Proposal> {
        try {
            const result = await db.insert(proposals).values(data).returning();
            return this.mapDbToProposal(result[0]);
        } catch (error) {
            console.error('Error creating proposal:', error);
            throw new Error('Failed to create proposal');
        }
    }

    async update(data: UpdateProposalData): Promise<Proposal> {
        try {
            const { id, ...updateData } = data;
            const result = await db
                .update(proposals)
                .set(updateData)
                .where(eq(proposals.id, id))
                .returning();
            return this.mapDbToProposal(result[0]);
        } catch (error) {
            console.error('Error updating proposal:', error);
            throw new Error('Failed to update proposal');
        }
    }

    async delete(id: string): Promise<void> {
        try {
            await db.delete(proposals).where(eq(proposals.id, id));
        } catch (error) {
            console.error('Error deleting proposal:', error);
            throw new Error('Failed to delete proposal');
        }
    }

    async search(query: string): Promise<Proposal[]> {
        try {
            const result = await db
                .select()
                .from(proposals)
                .where(
                    like(proposals.eventName, `%${query}%`)
                )
                .orderBy(desc(proposals.createdAt));
            return result.map(this.mapDbToProposal);
        } catch (error) {
            console.error('Error searching proposals:', error);
            throw new Error('Failed to search proposals');
        }
    }

    async getByStatus(status: string): Promise<Proposal[]> {
        try {
            const result = await db
                .select()
                .from(proposals)
                .where(eq(proposals.status, status))
                .orderBy(desc(proposals.createdAt));
            return result.map(this.mapDbToProposal);
        } catch (error) {
            console.error('Error fetching proposals by status:', error);
            throw new Error('Failed to fetch proposals by status');
        }
    }

    async getBySpeaker(speakerId: string): Promise<Proposal[]> {
        try {
            const result = await db
                .select()
                .from(proposals)
                .where(eq(proposals.speakerId, speakerId))
                .orderBy(desc(proposals.createdAt));
            return result.map(this.mapDbToProposal);
        } catch (error) {
            console.error('Error fetching proposals by speaker:', error);
            throw new Error('Failed to fetch proposals by speaker');
        }
    }

    async getByEventName(eventName: string): Promise<Proposal[]> {
        try {
            const result = await db
                .select()
                .from(proposals)
                .where(eq(proposals.eventName, eventName))
                .orderBy(desc(proposals.createdAt));
            return result.map(this.mapDbToProposal);
        } catch (error) {
            console.error('Error fetching proposals by event name:', error);
            throw new Error('Failed to fetch proposals by event name');
        }
    }

    async getCount(): Promise<number> {
        try {
            const result = await db.select({ count: proposals.id }).from(proposals);
            return result.length;
        } catch (error) {
            console.error('Error getting proposal count:', error);
            throw new Error('Failed to get proposal count');
        }
    }

    private mapDbToProposal(dbProposal: any): Proposal {
        return {
            id: dbProposal.id,
            eventName: dbProposal.eventName || undefined,
            speakerId: dbProposal.speakerId || undefined,
            details: dbProposal.details || undefined,
            status: dbProposal.status || undefined,
            pdfPath: dbProposal.pdfPath || undefined,
            submittedDate: dbProposal.submittedDate || undefined,
            createdAt: dbProposal.createdAt,
        };
    }
}

export const proposalsService = new ProposalsService();
