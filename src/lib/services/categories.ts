import { db } from "@/db";
import { categories } from "@/db/schema";
import { eq, desc, asc, like } from "drizzle-orm";

export interface Category {
    id: string;
    name: string;
    createdAt: Date;
}

export interface CreateCategoryData {
    name: string;
}

export interface UpdateCategoryData extends Partial<CreateCategoryData> {
    id: string;
}

export class CategoriesService {
    async getAll(): Promise<Category[]> {
        try {
            const result = await db.select().from(categories).orderBy(asc(categories.name));
            return result.map(this.mapDbToCategory);
        } catch (error) {
            console.error('Error fetching categories:', error);
            throw new Error('Failed to fetch categories');
        }
    }

    async getById(id: string): Promise<Category | null> {
        try {
            const result = await db.select().from(categories).where(eq(categories.id, id)).limit(1);
            return result.length > 0 ? this.mapDbToCategory(result[0]) : null;
        } catch (error) {
            console.error('Error fetching category:', error);
            throw new Error('Failed to fetch category');
        }
    }

    async create(data: CreateCategoryData): Promise<Category> {
        try {
            const result = await db.insert(categories).values(data).returning();
            return this.mapDbToCategory(result[0]);
        } catch (error) {
            console.error('Error creating category:', error);
            throw new Error('Failed to create category');
        }
    }

    async update(data: UpdateCategoryData): Promise<Category> {
        try {
            const { id, ...updateData } = data;
            const result = await db
                .update(categories)
                .set(updateData)
                .where(eq(categories.id, id))
                .returning();
            return this.mapDbToCategory(result[0]);
        } catch (error) {
            console.error('Error updating category:', error);
            throw new Error('Failed to update category');
        }
    }

    async delete(id: string): Promise<void> {
        try {
            await db.delete(categories).where(eq(categories.id, id));
        } catch (error) {
            console.error('Error deleting category:', error);
            throw new Error('Failed to delete category');
        }
    }

    async search(query: string): Promise<Category[]> {
        try {
            const result = await db
                .select()
                .from(categories)
                .where(
                    like(categories.name, `%${query}%`)
                )
                .orderBy(asc(categories.name));
            return result.map(this.mapDbToCategory);
        } catch (error) {
            console.error('Error searching categories:', error);
            throw new Error('Failed to search categories');
        }
    }

    async getCount(): Promise<number> {
        try {
            const result = await db.select({ count: categories.id }).from(categories);
            return result.length;
        } catch (error) {
            console.error('Error getting category count:', error);
            throw new Error('Failed to get category count');
        }
    }

    private mapDbToCategory(dbCategory: any): Category {
        return {
            id: dbCategory.id,
            name: dbCategory.name,
            createdAt: dbCategory.created_at,
        };
    }
}

export const categoriesService = new CategoriesService();
