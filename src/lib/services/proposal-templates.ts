import { db } from "@/db";
import { proposalTemplates } from "@/db/schema";
import { eq, desc, asc, like } from "drizzle-orm";

export interface ProposalTemplate {
    id: string;
    userId: string;
    name: string;
    coverPageTitle: string;
    coverPageSubtitle?: string;
    coverPageImageUrl?: string;
    aboutUsMission: string;

    // Colors
    primaryColor: string;
    secondaryColor: string;
    accentColor: string;
    textColor: string;
    backgroundColor: string;

    // Typography
    headingFont: string;
    bodyFont: string;
    fontSizeBase: number;
    lineHeight: string;

    // Layout
    pageMargin: number;
    sectionSpacing: number;
    headerHeight: number;
    footerHeight: number;

    // Page Structure
    includeCoverPage: boolean;
    includeAboutPage: boolean;
    includeEventDetails: boolean;
    includeSpeakerProfiles: boolean;
    includeInvestmentSummary: boolean;
    includeThankYouPage: boolean;

    // Content Options
    showSpeakerImages: boolean;
    showSpeakerBios: boolean;
    showSpeakerRates: boolean;
    showCompanyLogo: boolean;
    watermarkText?: string;

    // Advanced Layout
    layoutStyle: 'classic' | 'modern' | 'minimal' | 'creative';
    speakerLayout: 'grid' | 'list' | 'cards';
    imageStyle: 'square' | 'circle' | 'rounded';

    createdAt: Date;
    updatedAt: Date;
}

export interface CreateProposalTemplateData {
    userId: string;
    name: string;
    coverPageTitle: string;
    coverPageSubtitle?: string;
    coverPageImageUrl?: string;
    aboutUsMission: string;
    primaryColor?: string;
    secondaryColor?: string;
    accentColor?: string;
    textColor?: string;
    backgroundColor?: string;
    headingFont?: string;
    bodyFont?: string;
    fontSizeBase?: number;
    lineHeight?: string;
    pageMargin?: number;
    sectionSpacing?: number;
    headerHeight?: number;
    footerHeight?: number;
    includeCoverPage?: boolean;
    includeAboutPage?: boolean;
    includeEventDetails?: boolean;
    includeSpeakerProfiles?: boolean;
    includeInvestmentSummary?: boolean;
    includeThankYouPage?: boolean;
    showSpeakerImages?: boolean;
    showSpeakerBios?: boolean;
    showSpeakerRates?: boolean;
    showCompanyLogo?: boolean;
    watermarkText?: string;
    layoutStyle?: 'classic' | 'modern' | 'minimal' | 'creative';
    speakerLayout?: 'grid' | 'list' | 'cards';
    imageStyle?: 'square' | 'circle' | 'rounded';
}

export interface UpdateProposalTemplateData extends Partial<CreateProposalTemplateData> {
    id: string;
}

export class ProposalTemplatesService {
    async getAll(): Promise<ProposalTemplate[]> {
        try {
            const result = await db.select().from(proposalTemplates).orderBy(desc(proposalTemplates.updatedAt));
            return result.map(this.mapDbToTemplate);
        } catch (error) {
            console.error('Error fetching proposal templates:', error);
            throw new Error('Failed to fetch proposal templates');
        }
    }

    async getById(id: string): Promise<ProposalTemplate | null> {
        try {
            const result = await db.select().from(proposalTemplates).where(eq(proposalTemplates.id, id)).limit(1);
            return result.length > 0 ? this.mapDbToTemplate(result[0]) : null;
        } catch (error) {
            console.error('Error fetching proposal template:', error);
            throw new Error('Failed to fetch proposal template');
        }
    }

    async getByUserId(userId: string): Promise<ProposalTemplate[]> {
        try {
            const result = await db
                .select()
                .from(proposalTemplates)
                .where(eq(proposalTemplates.userId, userId))
                .orderBy(desc(proposalTemplates.updatedAt));
            return result.map(this.mapDbToTemplate);
        } catch (error) {
            console.error('Error fetching proposal templates by user:', error);
            throw new Error('Failed to fetch proposal templates by user');
        }
    }

    async create(data: CreateProposalTemplateData): Promise<ProposalTemplate> {
        try {
            const result = await db.insert(proposalTemplates).values(data).returning();
            return this.mapDbToTemplate(result[0]);
        } catch (error) {
            console.error('Error creating proposal template:', error);
            throw new Error('Failed to create proposal template');
        }
    }

    async update(data: UpdateProposalTemplateData): Promise<ProposalTemplate> {
        try {
            const { id, ...updateData } = data;
            const result = await db
                .update(proposalTemplates)
                .set({
                    ...updateData,
                    updatedAt: new Date(),
                })
                .where(eq(proposalTemplates.id, id))
                .returning();
            return this.mapDbToTemplate(result[0]);
        } catch (error) {
            console.error('Error updating proposal template:', error);
            throw new Error('Failed to update proposal template');
        }
    }

    async delete(id: string): Promise<void> {
        try {
            await db.delete(proposalTemplates).where(eq(proposalTemplates.id, id));
        } catch (error) {
            console.error('Error deleting proposal template:', error);
            throw new Error('Failed to delete proposal template');
        }
    }

    async search(query: string): Promise<ProposalTemplate[]> {
        try {
            const result = await db
                .select()
                .from(proposalTemplates)
                .where(
                    like(proposalTemplates.name, `%${query}%`)
                )
                .orderBy(desc(proposalTemplates.updatedAt));
            return result.map(this.mapDbToTemplate);
        } catch (error) {
            console.error('Error searching proposal templates:', error);
            throw new Error('Failed to search proposal templates');
        }
    }

    async getCount(): Promise<number> {
        try {
            const result = await db.select({ count: proposalTemplates.id }).from(proposalTemplates);
            return result.length;
        } catch (error) {
            console.error('Error getting proposal template count:', error);
            throw new Error('Failed to get proposal template count');
        }
    }

    private mapDbToTemplate(dbTemplate: any): ProposalTemplate {
        return {
            id: dbTemplate.id,
            userId: dbTemplate.userId,
            name: dbTemplate.name,
            coverPageTitle: dbTemplate.coverPageTitle,
            coverPageSubtitle: dbTemplate.coverPageSubtitle || undefined,
            coverPageImageUrl: dbTemplate.coverPageImageUrl || undefined,
            aboutUsMission: dbTemplate.aboutUsMission,
            primaryColor: dbTemplate.primaryColor,
            secondaryColor: dbTemplate.secondaryColor,
            accentColor: dbTemplate.accentColor,
            textColor: dbTemplate.textColor,
            backgroundColor: dbTemplate.backgroundColor,
            headingFont: dbTemplate.headingFont,
            bodyFont: dbTemplate.bodyFont,
            fontSizeBase: dbTemplate.fontSizeBase,
            lineHeight: dbTemplate.lineHeight,
            pageMargin: dbTemplate.pageMargin,
            sectionSpacing: dbTemplate.sectionSpacing,
            headerHeight: dbTemplate.headerHeight,
            footerHeight: dbTemplate.footerHeight,
            includeCoverPage: dbTemplate.includeCoverPage,
            includeAboutPage: dbTemplate.includeAboutPage,
            includeEventDetails: dbTemplate.includeEventDetails,
            includeSpeakerProfiles: dbTemplate.includeSpeakerProfiles,
            includeInvestmentSummary: dbTemplate.includeInvestmentSummary,
            includeThankYouPage: dbTemplate.includeThankYouPage,
            showSpeakerImages: dbTemplate.showSpeakerImages,
            showSpeakerBios: dbTemplate.showSpeakerBios,
            showSpeakerRates: dbTemplate.showSpeakerRates,
            showCompanyLogo: dbTemplate.showCompanyLogo,
            watermarkText: dbTemplate.watermarkText || undefined,
            layoutStyle: dbTemplate.layoutStyle,
            speakerLayout: dbTemplate.speakerLayout,
            imageStyle: dbTemplate.imageStyle,
            createdAt: dbTemplate.createdAt,
            updatedAt: dbTemplate.updatedAt,
        };
    }
}

export const proposalTemplatesService = new ProposalTemplatesService();
