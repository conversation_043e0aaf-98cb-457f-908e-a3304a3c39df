import { db } from "@/db";
import { employees } from "@/db/schema";
import { eq, desc, asc, like } from "drizzle-orm";

export interface Employee {
    id: string;
    name: string;
    email: string;
    role?: string;
    phone?: string;
    imageUrl?: string;
    createdAt: Date;
}

export interface CreateEmployeeData {
    name: string;
    email: string;
    role?: string;
    phone?: string;
    imageUrl?: string;
}

export interface UpdateEmployeeData extends Partial<CreateEmployeeData> {
    id: string;
}

export class EmployeesService {
    async getAll(): Promise<Employee[]> {
        try {
            const result = await db.select().from(employees).orderBy(asc(employees.name));
            return result.map(this.mapDbToEmployee);
        } catch (error) {
            console.error('Error fetching employees:', error);
            throw new Error('Failed to fetch employees');
        }
    }

    async getById(id: string): Promise<Employee | null> {
        try {
            const result = await db.select().from(employees).where(eq(employees.id, id)).limit(1);
            return result.length > 0 ? this.mapDbToEmployee(result[0]) : null;
        } catch (error) {
            console.error('Error fetching employee:', error);
            throw new Error('Failed to fetch employee');
        }
    }

    async create(data: CreateEmployeeData): Promise<Employee> {
        try {
            const result = await db.insert(employees).values(data).returning();
            return this.mapDbToEmployee(result[0]);
        } catch (error) {
            console.error('Error creating employee:', error);
            throw new Error('Failed to create employee');
        }
    }

    async update(data: UpdateEmployeeData): Promise<Employee> {
        try {
            const { id, ...updateData } = data;
            const result = await db
                .update(employees)
                .set(updateData)
                .where(eq(employees.id, id))
                .returning();
            return this.mapDbToEmployee(result[0]);
        } catch (error) {
            console.error('Error updating employee:', error);
            throw new Error('Failed to update employee');
        }
    }

    async delete(id: string): Promise<void> {
        try {
            await db.delete(employees).where(eq(employees.id, id));
        } catch (error) {
            console.error('Error deleting employee:', error);
            throw new Error('Failed to delete employee');
        }
    }

    async search(query: string): Promise<Employee[]> {
        try {
            const result = await db
                .select()
                .from(employees)
                .where(
                    like(employees.name, `%${query}%`)
                )
                .orderBy(asc(employees.name));
            return result.map(this.mapDbToEmployee);
        } catch (error) {
            console.error('Error searching employees:', error);
            throw new Error('Failed to search employees');
        }
    }

    async getByRole(role: string): Promise<Employee[]> {
        try {
            const result = await db
                .select()
                .from(employees)
                .where(eq(employees.role, role))
                .orderBy(asc(employees.name));
            return result.map(this.mapDbToEmployee);
        } catch (error) {
            console.error('Error fetching employees by role:', error);
            throw new Error('Failed to fetch employees by role');
        }
    }

    async getCount(): Promise<number> {
        try {
            const result = await db.select({ count: employees.id }).from(employees);
            return result.length;
        } catch (error) {
            console.error('Error getting employee count:', error);
            throw new Error('Failed to get employee count');
        }
    }

    private mapDbToEmployee(dbEmployee: any): Employee {
        return {
            id: dbEmployee.id,
            name: dbEmployee.name,
            email: dbEmployee.email,
            role: dbEmployee.role || undefined,
            phone: dbEmployee.phone || undefined,
            imageUrl: dbEmployee.imageUrl || undefined,
            createdAt: dbEmployee.createdAt,
        };
    }
}

export const employeesService = new EmployeesService();
