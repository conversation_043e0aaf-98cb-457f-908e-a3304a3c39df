import { db } from "@/db";
import { speakerImages, speakers } from "@/db/schema";
import { eq, desc, asc } from "drizzle-orm";

export interface SpeakerImage {
    id: string;
    speakerId: string;
    imageUrl: string;
    createdAt: Date;
}

export interface CreateSpeakerImageData {
    speakerId: string;
    imageUrl: string;
}

export interface UpdateSpeakerImageData extends Partial<CreateSpeakerImageData> {
    id: string;
}

export interface SpeakerImageWithSpeaker extends SpeakerImage {
    speaker?: {
        id: string;
        name: string;
        bio?: string;
        category?: string;
    };
}

export class SpeakerImagesService {
    async getAll(): Promise<SpeakerImage[]> {
        try {
            const result = await db.select().from(speakerImages).orderBy(desc(speakerImages.createdAt));
            return result.map(this.mapDbToSpeakerImage);
        } catch (error) {
            console.error('Error fetching speaker images:', error);
            throw new Error('Failed to fetch speaker images');
        }
    }

    async getById(id: string): Promise<SpeakerImage | null> {
        try {
            const result = await db.select().from(speakerImages).where(eq(speakerImages.id, id)).limit(1);
            return result.length > 0 ? this.mapDbToSpeakerImage(result[0]) : null;
        } catch (error) {
            console.error('Error fetching speaker image:', error);
            throw new Error('Failed to fetch speaker image');
        }
    }

    async getBySpeakerId(speakerId: string): Promise<SpeakerImage[]> {
        try {
            const result = await db
                .select()
                .from(speakerImages)
                .where(eq(speakerImages.speakerId, speakerId))
                .orderBy(desc(speakerImages.createdAt));
            return result.map(this.mapDbToSpeakerImage);
        } catch (error) {
            console.error('Error fetching speaker images by speaker:', error);
            throw new Error('Failed to fetch speaker images by speaker');
        }
    }

    async getWithSpeaker(id: string): Promise<SpeakerImageWithSpeaker | null> {
        try {
            const result = await db
                .select({
                    image: speakerImages,
                    speaker: speakers,
                })
                .from(speakerImages)
                .leftJoin(speakers, eq(speakerImages.speakerId, speakers.id))
                .where(eq(speakerImages.id, id))
                .limit(1);

            if (result.length === 0) return null;

            const image = this.mapDbToSpeakerImage(result[0].image);
            const speaker = result[0].speaker ? {
                id: result[0].speaker.id,
                name: result[0].speaker.name,
                bio: result[0].speaker.bio || undefined,
                category: result[0].speaker.category || undefined,
            } : undefined;

            return {
                ...image,
                speaker,
            };
        } catch (error) {
            console.error('Error fetching speaker image with speaker:', error);
            throw new Error('Failed to fetch speaker image with speaker');
        }
    }

    async create(data: CreateSpeakerImageData): Promise<SpeakerImage> {
        try {
            const result = await db.insert(speakerImages).values(data).returning();
            return this.mapDbToSpeakerImage(result[0]);
        } catch (error) {
            console.error('Error creating speaker image:', error);
            throw new Error('Failed to create speaker image');
        }
    }

    async update(data: UpdateSpeakerImageData): Promise<SpeakerImage> {
        try {
            const { id, ...updateData } = data;
            const result = await db
                .update(speakerImages)
                .set(updateData)
                .where(eq(speakerImages.id, id))
                .returning();
            return this.mapDbToSpeakerImage(result[0]);
        } catch (error) {
            console.error('Error updating speaker image:', error);
            throw new Error('Failed to update speaker image');
        }
    }

    async delete(id: string): Promise<void> {
        try {
            await db.delete(speakerImages).where(eq(speakerImages.id, id));
        } catch (error) {
            console.error('Error deleting speaker image:', error);
            throw new Error('Failed to delete speaker image');
        }
    }

    async deleteBySpeakerId(speakerId: string): Promise<void> {
        try {
            await db.delete(speakerImages).where(eq(speakerImages.speakerId, speakerId));
        } catch (error) {
            console.error('Error deleting speaker images by speaker:', error);
            throw new Error('Failed to delete speaker images by speaker');
        }
    }

    async getCount(): Promise<number> {
        try {
            const result = await db.select({ count: speakerImages.id }).from(speakerImages);
            return result.length;
        } catch (error) {
            console.error('Error getting speaker image count:', error);
            throw new Error('Failed to get speaker image count');
        }
    }

    private mapDbToSpeakerImage(dbImage: any): SpeakerImage {
        return {
            id: dbImage.id,
            speakerId: dbImage.speakerId,
            imageUrl: dbImage.imageUrl,
            createdAt: dbImage.createdAt,
        };
    }
}

export const speakerImagesService = new SpeakerImagesService();
