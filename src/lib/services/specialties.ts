import { db } from "@/db";
import { specialties } from "@/db/schema";
import { eq, desc, asc, like } from "drizzle-orm";

export interface Specialty {
    id: string;
    name: string;
    createdAt: Date;
}

export interface CreateSpecialtyData {
    name: string;
}

export interface UpdateSpecialtyData extends Partial<CreateSpecialtyData> {
    id: string;
}

export class SpecialtiesService {
    async getAll(): Promise<Specialty[]> {
        try {
            const result = await db.select().from(specialties).orderBy(asc(specialties.name));
            return result.map(this.mapDbToSpecialty);
        } catch (error) {
            console.error('Error fetching specialties:', error);
            throw new Error('Failed to fetch specialties');
        }
    }

    async getById(id: string): Promise<Specialty | null> {
        try {
            const result = await db.select().from(specialties).where(eq(specialties.id, id)).limit(1);
            return result.length > 0 ? this.mapDbToSpecialty(result[0]) : null;
        } catch (error) {
            console.error('Error fetching specialty:', error);
            throw new Error('Failed to fetch specialty');
        }
    }

    async create(data: CreateSpecialtyData): Promise<Specialty> {
        try {
            const result = await db.insert(specialties).values(data).returning();
            return this.mapDbToSpecialty(result[0]);
        } catch (error) {
            console.error('Error creating specialty:', error);
            throw new Error('Failed to create specialty');
        }
    }

    async update(data: UpdateSpecialtyData): Promise<Specialty> {
        try {
            const { id, ...updateData } = data;
            const result = await db
                .update(specialties)
                .set(updateData)
                .where(eq(specialties.id, id))
                .returning();
            return this.mapDbToSpecialty(result[0]);
        } catch (error) {
            console.error('Error updating specialty:', error);
            throw new Error('Failed to update specialty');
        }
    }

    async delete(id: string): Promise<void> {
        try {
            await db.delete(specialties).where(eq(specialties.id, id));
        } catch (error) {
            console.error('Error deleting specialty:', error);
            throw new Error('Failed to delete specialty');
        }
    }

    async search(query: string): Promise<Specialty[]> {
        try {
            const result = await db
                .select()
                .from(specialties)
                .where(
                    like(specialties.name, `%${query}%`)
                )
                .orderBy(asc(specialties.name));
            return result.map(this.mapDbToSpecialty);
        } catch (error) {
            console.error('Error searching specialties:', error);
            throw new Error('Failed to search specialties');
        }
    }

    async getCount(): Promise<number> {
        try {
            const result = await db.select({ count: specialties.id }).from(specialties);
            return result.length;
        } catch (error) {
            console.error('Error getting specialty count:', error);
            throw new Error('Failed to get specialty count');
        }
    }

    private mapDbToSpecialty(dbSpecialty: any): Specialty {
        return {
            id: dbSpecialty.id,
            name: dbSpecialty.name,
            createdAt: dbSpecialty.created_at,
        };
    }
}

export const specialtiesService = new SpecialtiesService();
