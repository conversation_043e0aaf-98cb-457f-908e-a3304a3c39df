import { db } from "@/db";
import { speakers } from "@/db/schema";
import { eq, asc, like } from "drizzle-orm";

export interface Speaker {
    id: string;
    name: string;
    bio?: string;
    category?: string;
    rate?: number;
    location?: string;
    experience?: string;
    availability?: 'Available' | 'Busy' | 'Unavailable';
    createdAt: Date;
}

export interface CreateSpeakerData {
    name: string;
    bio?: string;
    category?: string;
    rate?: number;
    location?: string;
    experience?: string;
    availability?: 'Available' | 'Busy' | 'Unavailable';
}

export interface UpdateSpeakerData extends Partial<CreateSpeakerData> {
    id: string;
}

export class SpeakersService {
    async getAll(): Promise<Speaker[]> {
        try {
            const result = await db.select().from(speakers).orderBy(asc(speakers.name));
            return result.map(this.mapDbToSpeaker);
        } catch (error) {
            console.error('Error fetching speakers:', error);
            throw new Error('Failed to fetch speakers');
        }
    }

    async getById(id: string): Promise<Speaker | null> {
        try {
            const result = await db.select().from(speakers).where(eq(speakers.id, id)).limit(1);
            return result.length > 0 ? this.mapDbToSpeaker(result[0]) : null;
        } catch (error) {
            console.error('Error fetching speaker:', error);
            throw new Error('Failed to fetch speaker');
        }
    }

    async create(data: CreateSpeakerData): Promise<Speaker> {
        try {
            const result = await db.insert(speakers).values(data).returning();
            return this.mapDbToSpeaker(result[0]);
        } catch (error) {
            console.error('Error creating speaker:', error);
            throw new Error('Failed to create speaker');
        }
    }

    async update(data: UpdateSpeakerData): Promise<Speaker> {
        try {
            const { id, ...updateData } = data;
            const result = await db
                .update(speakers)
                .set(updateData)
                .where(eq(speakers.id, id))
                .returning();
            return this.mapDbToSpeaker(result[0]);
        } catch (error) {
            console.error('Error updating speaker:', error);
            throw new Error('Failed to update speaker');
        }
    }

    async delete(id: string): Promise<void> {
        try {
            await db.delete(speakers).where(eq(speakers.id, id));
        } catch (error) {
            console.error('Error deleting speaker:', error);
            throw new Error('Failed to delete speaker');
        }
    }

    async search(query: string): Promise<Speaker[]> {
        try {
            const result = await db
                .select()
                .from(speakers)
                .where(
                    like(speakers.name, `%${query}%`)
                )
                .orderBy(asc(speakers.name));
            return result.map(this.mapDbToSpeaker);
        } catch (error) {
            console.error('Error searching speakers:', error);
            throw new Error('Failed to search speakers');
        }
    }

    async getByCategory(category: string): Promise<Speaker[]> {
        try {
            const result = await db
                .select()
                .from(speakers)
                .where(eq(speakers.category, category))
                .orderBy(asc(speakers.name));
            return result.map(this.mapDbToSpeaker);
        } catch (error) {
            console.error('Error fetching speakers by category:', error);
            throw new Error('Failed to fetch speakers by category');
        }
    }

    async getByAvailability(availability: 'Available' | 'Busy' | 'Unavailable'): Promise<Speaker[]> {
        try {
            const result = await db
                .select()
                .from(speakers)
                .where(eq(speakers.availability, availability))
                .orderBy(asc(speakers.name));
            return result.map(this.mapDbToSpeaker);
        } catch (error) {
            console.error('Error fetching speakers by availability:', error);
            throw new Error('Failed to fetch speakers by availability');
        }
    }

    async getCount(): Promise<number> {
        try {
            const result = await db.select({ count: speakers.id }).from(speakers);
            return result.length;
        } catch (error) {
            console.error('Error getting speaker count:', error);
            throw new Error('Failed to get speaker count');
        }
    }

    private mapDbToSpeaker(dbSpeaker: any): Speaker {
        return {
            id: dbSpeaker.id,
            name: dbSpeaker.name,
            bio: dbSpeaker.bio || undefined,
            category: dbSpeaker.category || undefined,
            rate: dbSpeaker.rate || undefined,
            location: dbSpeaker.location || undefined,
            experience: dbSpeaker.experience || undefined,
            availability: dbSpeaker.availability || undefined,
            createdAt: dbSpeaker.created_at,
        };
    }
}

export const speakersService = new SpeakersService();
