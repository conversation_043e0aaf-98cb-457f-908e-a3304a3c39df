
interface ExportToCsvOptions<T> {
  data: T[];
  filename: string;
  headers?: (keyof T)[];
}

export const exportToCsv = <T extends Record<string, any>>({
  data,
  filename,
  headers,
}: ExportToCsvOptions<T>) => {
  if (!data || data.length === 0) {
    console.error("No data to export.");
    return;
  }

  const columnHeaders = headers || (Object.keys(data[0]) as (keyof T)[]);

  const csvRows = data.map(item =>
    columnHeaders
      .map(header => {
        const value = item[header];
        if (value === null || value === undefined) {
          return '';
        }
        if (Array.isArray(value)) {
          return `"${value.join('; ')}"`;
        }
        if (typeof value === 'string' && (value.includes(',') || value.includes('"') || value.includes('\n'))) {
          return `"${value.replace(/"/g, '""')}"`;
        }
        return String(value);
      })
      .join(',')
  );

  const csvContent = [columnHeaders.join(','), ...csvRows].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  link.setAttribute('href', url);
  link.setAttribute('download', `${filename}.csv`);
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};
