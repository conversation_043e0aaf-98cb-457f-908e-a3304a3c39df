
import PptxGenJS from 'pptxgenjs';
import { Proposal } from '../types/proposal';

const toDataURL = (url: string): Promise<string> =>
  fetch(url)
    .then(response => {
      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
      }
      return response.blob();
    })
    .then(blob => new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => resolve(reader.result as string);
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    }));

// Function to validate and optimize image data
const validateImageData = (imageData: string): boolean => {
  if (!imageData || typeof imageData !== 'string') {
    return false;
  }

  // Check if it's a valid data URL
  if (!imageData.startsWith('data:image/')) {
    return false;
  }

  // Check if the data is not empty
  if (imageData.length < 100) {
    return false;
  }

  // Check if it's a supported image format - expanded to include all modern formats
  const supportedFormats = [
    'jpeg', 'jpg', 'png', 'gif', 'webp',
    'bmp', 'tiff', 'tif', 'svg', 'ico',
    'avif', 'heic', 'heif'
  ];
  const format = imageData.split(';')[0].split('/')[1];
  if (!supportedFormats.includes(format)) {
    console.warn('Unsupported image format detected:', format);
    return false;
  }

  return true;
};

const theme = {
  colors: {
    primary: '1e3a8a',
    secondary: '3b82f6',
    accent: 'f59e0b',
    textPrimary: '1f2937',
    textSecondary: '6b7280',
    background: 'FFFFFF',
    lightGray: 'f8fafc',
    darkBlue: '1e293b',
    overlay: '000000', // For background overlay
  },
  fonts: {
    primary: 'Inter',
    heading: 'Inter',
  }
};

export const generateProposalPPTX = async (proposal: Proposal) => {
  try {
    const pptx = new PptxGenJS();
    // Use standard 16:9 layout with default dimensions
    pptx.layout = 'LAYOUT_16x9';

    // Get image URLs
    const backgroundImageUrl = `${window.location.origin}/images/background.jpeg`;
    const logoImageUrl = `${window.location.origin}/images/logo.png`;
    const leftSideImageUrl = `${window.location.origin}/images/image.png`;
    const partnersImageUrl = `${window.location.origin}/images/partners-image.png`;
    const aboutRightImageUrl = `${window.location.origin}/images/about-us-side-image.png`;

    // Convert images to base64
    let backgroundImageBase64: string | null = null;
    let logoImageBase64: string | null = null;
    let leftSideImageBase64: string | null = null;
    let aboutRightImageBase64: string | null = null;
    let partnersImageBase64: string | null = null;

    try {
      backgroundImageBase64 = await toDataURL(backgroundImageUrl);
      logoImageBase64 = await toDataURL(logoImageUrl);
      leftSideImageBase64 = await toDataURL(leftSideImageUrl);
      aboutRightImageBase64 = await toDataURL(aboutRightImageUrl);
      partnersImageBase64 = await toDataURL(partnersImageUrl);
    } catch (error) {
      console.warn('Could not load images:', error);
    }

    // Define slide master with left-side accent image (full height, preserve aspect ratio)
    pptx.defineSlideMaster({
      title: 'MASTER_SLIDE',
      background: { color: theme.colors.background },
      objects: leftSideImageBase64 ? [
        {
          image: {
            data: leftSideImageBase64,
            x: 0, y: 0,
            h: '100%'
          }
        }
      ] : []
    });

    // 1. Cover Page
    const coverSlide = pptx.addSlide();

    // Add background image
    if (backgroundImageBase64) {
      coverSlide.addImage({
        data: backgroundImageBase64,
        x: 0, y: 0, w: '100%', h: '100%'
      });
    } else {
      coverSlide.addShape(pptx.ShapeType.rect, {
        x: 0, y: 0, w: '100%', h: '100%',
        fill: { color: theme.colors.primary }
      });
    }

    // Add overlay
    coverSlide.addShape(pptx.ShapeType.rect, {
      x: 0, y: 0, w: '100%', h: '100%',
      fill: { color: theme.colors.overlay, transparency: 60 }
    });

    // Add logo (perfectly centered) - same dimensions as thank you page
    if (logoImageBase64) {
      coverSlide.addImage({
        data: logoImageBase64,
        x: '41.5%', y: '30%', w: '17%', h: '16%'
      });
    }

    // Add main text (perfectly centered) - minimal margin from logo
    coverSlide.addText('The #1 Speakers Bureau in the MENA Region since 2016', {
      x: '10%', y: '48%', w: '80%', h: '8%',
      align: 'center',
      fontSize: 16,
      color: 'FFFFFF',
      fontFace: theme.fonts.primary,
      bold: false
    });

    // 2. Partners Page (after cover)
    const partnersSlide = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
    if (partnersImageBase64) {
      partnersSlide.addImage({
        data: partnersImageBase64,
        x: '18%', y: 0, w: '82%', h: '100%'
      });
    }

    // 3. About Us
    const aboutSlide = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
    aboutSlide.addText('About Us', {
      x: '18%', y: '7%', w: '56%', h: '10%',
      fontSize: 32, bold: false, color: theme.colors.primary,
      fontFace: theme.fonts.primary
    });

    // Removed underline line under About Us title

    // Right-side image for About Us (nearly full height, centered vertically in the right column)
    if (aboutRightImageBase64) {
      aboutSlide.addImage({
        data: aboutRightImageBase64,
        x: '76%', y: '5%', w: '24%', h: '90%'
      });
    }

    const aboutText = `MENA Speakers is a highly acclaimed agency offering customized solutions for various speaking engagements. Established in 2016, we have become the foremost speakers' agency in the Middle East, offering services that can compete with international agencies. Our exceptional track record is a testament to our unwavering commitment to delivering top notch speakers who constantly raise the bar.

Our expert orators can innovate, facilitate, moderate, and motivate audiences across different events, including keynote speeches, educational seminars, and one-on-one meetings. Under the leadership of Saana Azzam, an award-winning economist, MENA Speakers represents the best and brightest in the region, including VIPs such as Prince Salman from Saudi Arabia, Mohammed Qahtani, world champion in public speaking, and Muna AbuSulayman.

We have also brought in renowned international speakers such as Fed Reserve Chairman Ben Bernanke, Janet Yellen, and David Meltzer. Our exclusive speaker roster includes Mathew Knowles, the father and agent of Beyoncé, Joe Foster, the founder of Reebok, and Hala Gorani, a CNN anchor. We have an impressive client base, which includes Gulf Ministries, Kellogg's Company, Standard Chartered Bank, NEOM, Misk, Al Ula, and many more.

In short, MENA Speakers is a trusted and reliable agency that provides exceptional speakers and services to meet the diverse needs of our clients.`;

    aboutSlide.addText(aboutText, {
      x: '18%', y: '42%', w: '54%', h: '33%',
      fontSize: 10, color: theme.colors.textPrimary,
      fontFace: theme.fonts.primary
    });

    // 4. Speakers Divider
    const speakersSlide = pptx.addSlide();

    // Add background image
    if (backgroundImageBase64) {
      speakersSlide.addImage({
        data: backgroundImageBase64,
        x: 0, y: 0, w: '100%', h: '100%'
      });
    }

    // Add overlay
    speakersSlide.addShape(pptx.ShapeType.rect, {
      x: 0, y: 0, w: '100%', h: '100%',
      fill: { color: theme.colors.overlay, transparency: 40 }
    });

    speakersSlide.addText('Speakers', {
      x: '10%', y: '40%', w: '80%', h: '20%',
      align: 'center',
      fontSize: 48,
      color: 'FFFFFF',
      fontFace: theme.fonts.heading,
      bold: true
    });

    // 5. Speaker Profile Pages
    if (!proposal.speakers || proposal.speakers.length === 0) {
      console.warn('No speakers found in proposal for PPTX generation');
      // Add a placeholder slide indicating no speakers
      const noSpeakersSlide = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
      noSpeakersSlide.addText('No Speakers Selected', {
        x: '20%', y: '40%', w: '60%', h: '20%',
        fontSize: 24, bold: true, color: theme.colors.textSecondary,
        fontFace: theme.fonts.primary,
        align: 'center'
      });
    } else {
      console.log('Generating slides for', proposal.speakers.length, 'speakers');

      for (const speakerData of proposal.speakers) {
        if (!speakerData || !speakerData.speaker) {
          console.warn('Invalid speaker data:', speakerData);
          continue;
        }

        const speaker = speakerData.speaker;
        const speakerSlide = pptx.addSlide({ masterName: 'MASTER_SLIDE' });

        // Speaker Name (Title) - positioned over the bio section
        speakerSlide.addText(speaker.name || 'Unknown Speaker', {
          x: '20%', y: '8%', w: '50%', h: '10%',
          fontSize: 28, bold: true, color: theme.colors.primary,
          fontFace: theme.fonts.primary
        });

        // Speaker Bio (Content under title) - wider bio section, avoiding left side image
        speakerSlide.addText(speaker.bio || 'Professional speaker with extensive experience.', {
          x: '20%', y: '20%', w: '50%', h: '70%',
          fontSize: 14, color: theme.colors.textPrimary,
          fontFace: theme.fonts.primary,
          valign: 'top'
        });

        // Add speaker image (reduced height to prevent stretching)
        if (speaker.image) {
          try {
            let speakerImageBase64: string;

            // Check if the image is already a data URL (base64)
            if (speaker.image.startsWith('data:image/')) {
              speakerImageBase64 = speaker.image;
              console.log('PPTX Generator - Using base64 image for:', speaker.name);

              // Extract image format for debugging
              const format = speakerImageBase64.split(';')[0].split('/')[1];
              console.log('PPTX Generator - Image format:', format, 'for:', speaker.name);

              // Log image size for debugging
              if (speakerImageBase64.length > 500000) {
                console.log('PPTX Generator - Large image detected for:', speaker.name, 'Size:', Math.round(speakerImageBase64.length / 1024), 'KB');
              }
            } else {
              // Convert URL to base64
              speakerImageBase64 = await toDataURL(speaker.image);
              console.log('PPTX Generator - Converted URL to base64 for:', speaker.name);
            }

            // Validate the base64 data using our validation function
            if (!validateImageData(speakerImageBase64)) {
              throw new Error('Invalid or unsupported image format');
            }

            console.log('PPTX Generator - Image data length:', speakerImageBase64.length);
            console.log('PPTX Generator - Image data starts with:', speakerImageBase64.substring(0, 50));

            // For WebP and other modern formats, try to ensure compatibility
            const imageFormat = speakerImageBase64.split(';')[0].split('/')[1];
            if (['webp', 'avif', 'heic', 'heif'].includes(imageFormat)) {
              console.log('PPTX Generator - Modern format detected:', imageFormat, 'for:', speaker.name);
              console.log('PPTX Generator - Attempting to add to PPTX...');

              // Special handling for WebP - some PPTX generators have issues with WebP
              if (imageFormat === 'webp') {
                console.log('PPTX Generator - WebP format detected, ensuring compatibility for:', speaker.name);
                // The pptxgenjs library should handle WebP, but let's log for debugging
              }
            }

            speakerSlide.addImage({
              data: speakerImageBase64,
              x: '72%', y: '15%', w: '28%', h: '45%'
              // Reduced height from 55% to 45% to prevent stretching
            });

            console.log('PPTX Generator - Successfully added image for:', speaker.name);
          } catch (error) {
            console.error('Could not load speaker image for:', speaker.name, 'Error:', error);
            console.error('Image data:', speaker.image);

            // Log the specific error details
            if (error instanceof Error) {
              console.error('Error message:', error.message);
              console.error('Error stack:', error.stack);
            }

            // Try to add a placeholder or fallback
            try {
              // Add a placeholder shape instead of the image
              speakerSlide.addShape(pptx.ShapeType.rect, {
                x: '72%', y: '15%', w: '28%', h: '45%',
                fill: { color: 'E5E7EB' },
                line: { color: '9CA3AF', width: 1 }
              });

              speakerSlide.addText('Image\nNot\nAvailable', {
                x: '72%', y: '15%', w: '28%', h: '45%',
                fontSize: 12, color: '6B7280',
                fontFace: theme.fonts.primary,
                align: 'center',
                valign: 'middle'
              });

              console.log('PPTX Generator - Added placeholder for:', speaker.name);
            } catch (placeholderError) {
              console.error('Could not add placeholder for:', speaker.name, placeholderError);
            }
          }
        } else {
          console.log('PPTX Generator - No image provided for:', speaker.name);
        }

        // Fee and Location section positioned under the image
        const fee = speaker.rate || 0;
        const location = speaker.location || 'International';

        // Add info box under the image (not overlaying)
        // Positioned below the 45% height image
        speakerSlide.addShape(pptx.ShapeType.rect, {
          x: '72%', y: '62%', w: '28%', h: '20%',
          fill: { color: theme.colors.primary },
          line: { color: theme.colors.secondary, width: 1 }
        });

        // Fee title (under the image)
        speakerSlide.addText('Fee:', {
          x: '72%', y: '63%', w: '28%', h: '2.5%',
          fontSize: 10, color: 'FFFFFF',
          fontFace: theme.fonts.primary,
          bold: true,
          align: 'center',
          valign: 'middle'
        });

        // Fee amount - just the dollar amount
        speakerSlide.addText(`$${fee.toLocaleString()}`, {
          x: '72%', y: '65.5%', w: '28%', h: '3%',
          fontSize: 14, color: 'FFFFFF',
          fontFace: theme.fonts.primary,
          bold: true,
          align: 'center',
          valign: 'middle'
        });

        // Including text (if present) - smaller and separate line
        if (speaker.including) {
          speakerSlide.addText(`+ ${speaker.including}`, {
            x: '72%', y: '68.5%', w: '28%', h: '2.5%',
            fontSize: 9, color: 'FFFFFF',
            fontFace: theme.fonts.primary,
            align: 'center',
            valign: 'middle'
          });
        }

        // Location title and value (under the image)
        speakerSlide.addText('Location:', {
          x: '72%', y: '72%', w: '28%', h: '2.5%',
          fontSize: 9, color: 'FFFFFF',
          fontFace: theme.fonts.primary,
          bold: true,
          align: 'center',
          valign: 'middle'
        });

        speakerSlide.addText(location, {
          x: '72%', y: '74.5%', w: '28%', h: '3%',
          fontSize: 11, color: 'FFFFFF',
          fontFace: theme.fonts.primary,
          align: 'center',
          valign: 'middle'
        });
      }
    }

    // 6. Case Study Divider
    const caseStudySlide = pptx.addSlide();

    // Add background image
    if (backgroundImageBase64) {
      caseStudySlide.addImage({
        data: backgroundImageBase64,
        x: 0, y: 0, w: '100%', h: '100%'
      });
    }

    // Add overlay
    caseStudySlide.addShape(pptx.ShapeType.rect, {
      x: 0, y: 0, w: '100%', h: '100%',
      fill: { color: theme.colors.overlay, transparency: 40 }
    });

    caseStudySlide.addText('Case Studies', {
      x: '10%', y: '40%', w: '80%', h: '20%',
      align: 'center',
      fontSize: 48,
      color: 'FFFFFF',
      fontFace: theme.fonts.heading,
      bold: true
    });

    // 7. Case Study Content Pages (5 images)
    const caseStudyImageUrls = [1, 2, 3, 4, 5].map((i) => `${window.location.origin}/images/case-study-${i}.png`);
    const caseStudyImageBase64s: (string | null)[] = [];
    for (const url of caseStudyImageUrls) {
      try {
        caseStudyImageBase64s.push(await toDataURL(url));
      } catch (e) {
        console.warn('Could not load case study image:', url, e);
        caseStudyImageBase64s.push(null);
      }
    }

    for (const img of caseStudyImageBase64s) {
      const csSlide = pptx.addSlide({ masterName: 'MASTER_SLIDE' });
      if (img) {
        csSlide.addImage({
          data: img,
          x: '18%', y: '5%', w: '82%', h: '90%'
        });
      }
    }

    // 8. Thank You Page
    const thankYouSlide = pptx.addSlide();

    // Add background image (cover page background)
    if (backgroundImageBase64) {
      thankYouSlide.addImage({
        data: backgroundImageBase64,
        x: 0, y: 0, w: '100%', h: '100%'
      });
    }

    // Add overlay for better text readability
    thankYouSlide.addShape(pptx.ShapeType.rect, {
      x: 0, y: 0, w: '100%', h: '100%',
      fill: { color: theme.colors.overlay, transparency: 20 }
    });

    // Left column - Logo and tagline
    if (logoImageBase64) {
      thankYouSlide.addImage({
        data: logoImageBase64,
        x: '15%', y: '25%', w: '20%', h: '20%'
      });
    }

    thankYouSlide.addText('The #1 Speakers Bureau in the MENA Region since 2016', {
      x: '10%', y: '47%', w: '30%', h: '15%',
      fontSize: 14, color: 'FFFFFF',
      fontFace: theme.fonts.primary,
      align: 'center'
    });

    // Right column - Thank you and contact info
    thankYouSlide.addText('Thank you', {
      x: '55%', y: '20%', w: '35%', h: '15%',
      fontSize: 42, color: 'FFFFFF',
      fontFace: theme.fonts.primary,
      bold: true
    });

    thankYouSlide.addText('Book professional speakers, MCs, moderators', {
      x: '55%', y: '35%', w: '35%', h: '10%',
      fontSize: 12, color: 'FFFFFF',
      fontFace: theme.fonts.primary
    });

    thankYouSlide.addText('Contact', {
      x: '55%', y: '55%', w: '35%', h: '8%',
      fontSize: 28, color: 'FFFFFF',
      fontFace: theme.fonts.primary,
      bold: true
    });

    // Get employee contact information from proposal, fallback to defaults
    const employeeContact = proposal.employeeContact;
    console.log('PPTX Generator - Employee Contact:', employeeContact);
    console.log('PPTX Generator - Full Proposal:', proposal);

    const contactName = employeeContact?.name || 'Abdulghafour Alsamman';
    const contactEmail = employeeContact?.email || '<EMAIL>';
    const contactPhone = employeeContact?.phone || '+966 504456649';
    const website = 'www.mena-speakers.com'; // Website stays static as requested

    const contactInfo = `${contactName}
Email: ${contactEmail}
Phone: ${contactPhone}
Website: ${website}`;

    thankYouSlide.addText(contactInfo, {
      x: '55%', y: '65%', w: '35%', h: '25%',
      fontSize: 12, color: 'FFFFFF',
      fontFace: theme.fonts.primary
    });

    // Generate and download
    const eventNameForFile = proposal.event?.eventName || 'untitled-event';
    const fileName = `mena-speakers-proposal-${eventNameForFile.replace(/\s+/g, '-').toLowerCase()}-${Date.now()}.pptx`;
    await pptx.writeFile({ fileName });
  } catch (error) {
    console.error("Error during PPTX generation:", error);
    throw error;
  }
};