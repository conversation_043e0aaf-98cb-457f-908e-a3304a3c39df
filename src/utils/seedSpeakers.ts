import { db } from '@/db';
import { speakers } from '@/db/schema';

export const seedSpeakers = [
  {
    name: "Dr. <PERSON>",
    bio: "Leading expert in artificial intelligence and machine learning with over 15 years of experience in developing cutting-edge AI solutions for Fortune 500 companies.",
    category: "Technology",
    rate: 25000,
    location: "San Francisco, CA",
    experience: "15+ years in AI/ML",
    availability: "Available" as const,
  },
  {
    name: "<PERSON>",
    bio: "Cybersecurity specialist and former government consultant with expertise in protecting critical infrastructure and implementing security protocols for major corporations.",
    category: "Technology",
    rate: 22000,
    location: "Washington, DC",
    experience: "12+ years in cybersecurity",
    availability: "Available" as const,
  },
  {
    name: "<PERSON>",
    bio: "Blockchain and cryptocurrency expert who has advised numerous startups and established companies on implementing blockchain solutions and digital asset strategies.",
    category: "Technology",
    rate: 20000,
    location: "Miami, FL",
    experience: "10+ years in blockchain",
    availability: "Busy" as const,
  },
  {
    name: "<PERSON>",
    bio: "Strategic leadership consultant with a proven track record of transforming organizations and developing high-performing executive teams across various industries.",
    category: "Leadership",
    rate: 28000,
    location: "New York, NY",
    experience: "18+ years in leadership",
    availability: "Available" as const,
  },
  {
    name: "<PERSON>",
    bio: "Sales and business development expert who has helped companies increase revenue by 300% through innovative sales strategies and team optimization.",
    category: "Business",
    rate: 24000,
    location: "Chicago, IL",
    experience: "14+ years in sales",
    availability: "Available" as const,
  },
  {
    name: "Lisa Park",
    bio: "Entrepreneur and startup advisor who has founded and sold three successful companies, now helping other entrepreneurs navigate the startup journey.",
    category: "Business",
    rate: 26000,
    location: "Austin, TX",
    experience: "16+ years in entrepreneurship",
    availability: "Busy" as const,
  },
  {
    name: "Dr. Robert Kim",
    bio: "Wellness and mindfulness expert with a background in psychology, helping professionals achieve work-life balance and mental well-being in high-stress environments.",
    category: "Wellness",
    rate: 18000,
    location: "Los Angeles, CA",
    experience: "13+ years in wellness",
    availability: "Available" as const,
  },
  {
    name: "Amanda Foster",
    bio: "Performance psychologist and sports consultant who works with athletes and business professionals to optimize performance and achieve peak mental states.",
    category: "Wellness",
    rate: 20000,
    location: "Denver, CO",
    experience: "11+ years in psychology",
    availability: "Available" as const,
  },
  {
    name: "Dr. Carlos Mendez",
    bio: "Educational technology innovator who has revolutionized learning experiences in corporate and academic settings through cutting-edge digital solutions.",
    category: "Education",
    rate: 22000,
    location: "Seattle, WA",
    experience: "15+ years in edtech",
    availability: "Available" as const,
  },
  {
    name: "Rachel Green",
    bio: "Corporate training specialist who has developed and delivered training programs for over 100 companies, improving employee performance and retention.",
    category: "Education",
    rate: 19000,
    location: "Boston, MA",
    experience: "12+ years in training",
    availability: "Available" as const,
  },
  {
    name: "James Wilson",
    bio: "Motivational speaker and life coach who has inspired millions through his personal story of overcoming adversity and achieving extraordinary success.",
    category: "Motivation",
    rate: 30000,
    location: "Nashville, TN",
    experience: "20+ years in motivation",
    availability: "Busy" as const,
  },
  {
    name: "Maria Garcia",
    bio: "Resilience and mental toughness expert who has trained military personnel, athletes, and business leaders to perform under extreme pressure.",
    category: "Motivation",
    rate: 25000,
    location: "San Diego, CA",
    experience: "17+ years in resilience training",
    availability: "Available" as const,
  },
];

export const seedSpeakersIfEmpty = async () => {
  try {
    // Check if speakers already exist using Drizzle
    const existingSpeakers = await db.select({ id: speakers.id }).from(speakers).limit(1);

    // If speakers already exist, don't seed
    if (existingSpeakers && existingSpeakers.length > 0) {
      console.log('Speakers already exist, skipping seed');
      return true;
    }

    // Insert sample speakers using Drizzle - insert one by one to avoid type issues
    for (const speaker of seedSpeakers) {
      await db.insert(speakers).values(speaker);
    }

    console.log('Successfully seeded speakers');
    return true;
  } catch (error) {
    console.error('Error in seedSpeakersIfEmpty:', error);
    return false;
  }
};
