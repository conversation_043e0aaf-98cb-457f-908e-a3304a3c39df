
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { Proposal, ProposalTemplateSettings } from '../types/proposal';
import React from 'react';
import { createRoot } from 'react-dom/client';
import { ProposalPDFTemplate } from '../components/ProposalPDFTemplate';


export const generateProposalPDF = async (
  proposal: Proposal,
  templateSettings: ProposalTemplateSettings | null | undefined
): Promise<string> => {
  // Create a temporary div for rendering
  const tempDiv = document.createElement('div');
  tempDiv.style.position = 'absolute';
  tempDiv.style.left = '-9999px'; // Position off-screen
  tempDiv.style.width = '210mm'; // A4 width

  document.body.appendChild(tempDiv);

  // Create a root for React 18
  const root = createRoot(tempDiv);

  try {
    // Render the React component
    await new Promise<void>((resolve) => {
      root.render(React.createElement(ProposalPDFTemplate, { proposal }));
      // Give time for React to render
      setTimeout(resolve, 1000);
    });

    const pdf = new jsPDF('p', 'mm', 'a4');
    const pdfWidth = 210;
    const pdfHeight = 297;

    const pages = tempDiv.querySelectorAll<HTMLElement>('.pdf-page');

    for (let i = 0; i < pages.length; i++) {
      const page = pages[i];
      if (i > 0) {
        pdf.addPage();
      }

      // Ensure all <img> elements on this page have finished loading before capture
      const imgElements = Array.from(page.querySelectorAll<HTMLImageElement>('img'));
      await Promise.all(
        imgElements.map((img) =>
          img.complete
            ? Promise.resolve()
            : new Promise<void>((resolve) => {
              const done = () => resolve();
              img.addEventListener('load', done, { once: true });
              img.addEventListener('error', done, { once: true });
              // Fallback timeout to avoid hanging forever
              setTimeout(done, 3000);
            })
        )
      );

      const canvas = await html2canvas(page, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: null,
      });

      const imgData = canvas.toDataURL('image/png');
      pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight, undefined, 'FAST');
    }

    // Download the PDF for the user
    const eventName = proposal.event?.eventName || 'untitled-event';
    const fileName = `speaker-proposal-${eventName.replace(/\s+/g, '-').toLowerCase()}-${Date.now()}.pdf`;
    pdf.save(fileName);

    // Note: PDF storage functionality removed - Supabase storage no longer available
    // You can implement alternative storage solutions like AWS S3, Cloudinary, or local storage
    console.log('PDF generated successfully. Storage functionality needs to be implemented.');

    return fileName;

  } catch (error) {
    console.error("Error generating or uploading PDF:", error);
    throw error;
  } finally {
    // Clean up
    root.unmount();
    if (document.body.contains(tempDiv)) {
      document.body.removeChild(tempDiv);
    }
  }
};
