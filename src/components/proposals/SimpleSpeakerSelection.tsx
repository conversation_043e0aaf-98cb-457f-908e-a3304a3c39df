import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Users,
  Search,
  MapPin,
  DollarSign,
  Star,
  Filter,
  CheckCircle2,
  User,
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface SimpleSpeakerSelectionProps {
  speakers: any[];
  selectedSpeakers: string[];
  setSelectedSpeakers: (speakers: string[]) => void;
}

export const SimpleSpeakerSelection = ({
  speakers,
  selectedSpeakers,
  setSelectedSpeakers,
}: SimpleSpeakerSelectionProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [categoryFilter, setCategoryFilter] = useState<string>("all");
  const [showSelected, setShowSelected] = useState(false);

  const handleSpeakerToggle = (speakerId: string) => {
    if (selectedSpeakers.includes(speakerId)) {
      setSelectedSpeakers(selectedSpeakers.filter((id) => id !== speakerId));
    } else {
      setSelectedSpeakers([...selectedSpeakers, speakerId]);
    }
  };

  // Get unique categories
  const categories = Array.from(
    new Set(speakers.map((s) => s.category).filter(Boolean))
  );

  // Filter speakers based on search and category
  const filteredSpeakers = speakers.filter((speaker) => {
    const matchesSearch =
      speaker.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      speaker.bio?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory =
      categoryFilter === "all" || speaker.category === categoryFilter;
    const matchesSelected =
      !showSelected || selectedSpeakers.includes(speaker.id);

    return matchesSearch && matchesCategory && matchesSelected;
  });

  const getAvailabilityColor = (availability: string) => {
    switch (availability?.toLowerCase()) {
      case "available":
        return "bg-green-100 text-green-800";
      case "busy":
        return "bg-yellow-100 text-yellow-800";
      case "unavailable":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatRate = (rate: number | null) => {
    if (!rate) return "Rate on request";
    return `$${rate.toLocaleString()}`;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Select Speakers</span>
            {selectedSpeakers.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {selectedSpeakers.length} selected
              </Badge>
            )}
          </div>
        </CardTitle>

        {/* Selected Speakers Summary - Compact */}
        {selectedSpeakers.length > 0 && (
          <div className="bg-primary/5 rounded-lg p-3 border">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium text-sm">Selected Speakers</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSelectedSpeakers([])}
                className="h-6 px-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-100"
              >
                Clear All
              </Button>
            </div>
            <div className="flex gap-2 overflow-x-auto pb-1">
              {selectedSpeakers.map((speakerId) => {
                const speaker = speakers.find((s) => s.id === speakerId);
                if (!speaker) return null;
                return (
                  <div
                    key={speakerId}
                    className="flex-shrink-0 w-36 p-2 bg-background rounded border relative"
                  >
                    <div className="flex items-center gap-2">
                      <Avatar className="h-5 w-5">
                        <AvatarImage src={speaker.image} alt={speaker.name} />
                        <AvatarFallback className="text-xs">
                          {speaker.name
                            ?.split(" ")
                            .map((n: string) => n[0])
                            .join("")
                            .toUpperCase() || "SP"}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <p className="text-xs font-medium truncate">
                          {speaker.name}
                        </p>
                        <p className="text-xs text-muted-foreground truncate">
                          {speaker.category}
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleSpeakerToggle(speakerId)}
                        className="h-5 w-5 p-0 text-red-600 hover:text-red-700 hover:bg-red-100 absolute -top-1 -right-1"
                      >
                        ×
                      </Button>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Search and Filter Controls */}
        <div className="space-y-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
                              placeholder="Search speakers by name or bio..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>

          <div className="flex gap-2 flex-wrap">
            <Select value={categoryFilter} onValueChange={setCategoryFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button
              variant={showSelected ? "default" : "outline"}
              size="sm"
              onClick={() => setShowSelected(!showSelected)}
              className="flex items-center gap-2"
            >
              <Filter className="h-4 w-4" />
              {showSelected ? "Show All" : "Show Selected"}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {speakers.length === 0 ? (
          <div className="text-center py-8">
            <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              No speakers available. Please add speakers first.
            </p>
          </div>
        ) : filteredSpeakers.length === 0 ? (
          <div className="text-center py-8">
            <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">
              No speakers match your search criteria.
            </p>
          </div>
        ) : (
          <div className="grid gap-4 max-h-[70vh] overflow-y-auto grid-cols-1 md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 2xl:grid-cols-3">
            {filteredSpeakers.map((speaker) => {
              const isSelected = selectedSpeakers.includes(speaker.id);
              return (
                <Card
                  key={speaker.id}
                  className={`cursor-pointer transition-all hover:shadow-md ${
                    isSelected ? "ring-2 ring-primary bg-primary/5" : ""
                  }`}
                  onClick={() => handleSpeakerToggle(speaker.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-4">
                      {/* Avatar and Checkbox */}
                      <div className="relative flex-shrink-0">
                        <Avatar className="h-12 w-12 sm:h-16 sm:w-16">
                          <AvatarImage src={speaker.image} alt={speaker.name} />
                          <AvatarFallback className="text-xs sm:text-sm">
                            {speaker.name
                              ?.split(" ")
                              .map((n: string) => n[0])
                              .join("")
                              .toUpperCase() || "SP"}
                          </AvatarFallback>
                        </Avatar>
                        {isSelected && (
                          <CheckCircle2 className="absolute -top-1 -right-1 sm:-top-2 sm:-right-2 h-5 w-5 sm:h-6 sm:w-6 text-primary bg-white rounded-full border-2 border-white" />
                        )}
                      </div>

                      {/* Speaker Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-base sm:text-lg leading-tight truncate">
                              {speaker.name}
                            </h3>
                            <div className="flex items-center gap-2 mt-1">
                              {speaker.category && (
                                <Badge variant="outline" className="text-xs">
                                  {speaker.category}
                                </Badge>
                              )}
                              {speaker.availability && (
                                <Badge
                                  className={`text-xs ${getAvailabilityColor(
                                    speaker.availability
                                  )}`}
                                >
                                  {speaker.availability}
                                </Badge>
                              )}
                            </div>
                          </div>

                          <Checkbox
                            checked={isSelected}
                            onChange={() => handleSpeakerToggle(speaker.id)}
                            className="mt-1"
                          />
                        </div>

                        {speaker.bio && (
                          <p className="text-xs sm:text-sm text-muted-foreground mt-2 line-clamp-2 leading-relaxed">
                            {speaker.bio}
                          </p>
                        )}

                        <div className="flex items-center gap-2 sm:gap-4 mt-2 sm:mt-3 text-xs text-muted-foreground flex-wrap">
                          {speaker.location && (
                            <div className="flex items-center gap-1">
                              <MapPin className="h-3 w-3 flex-shrink-0" />
                              <span className="truncate">
                                {speaker.location}
                              </span>
                            </div>
                          )}

                          <div className="flex items-center gap-1">
                            <DollarSign className="h-3 w-3 flex-shrink-0" />
                            <span className="truncate">
                              {formatRate(speaker.rate)}
                            </span>
                          </div>

                          {speaker.experience && (
                            <div className="flex items-center gap-1">
                              <Star className="h-3 w-3 flex-shrink-0" />
                              <span className="truncate">
                                {speaker.experience}
                              </span>
                            </div>
                          )}
                        </div>


                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
