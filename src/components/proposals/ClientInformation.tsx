
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { FileText } from "lucide-react";

interface ClientInformationProps {
  clientInfo: {
    name: string;
    email: string;
    company: string;
    phone: string;
    notes: string;
  };
  setClientInfo: (info: {
    name: string;
    email: string;
    company: string;
    phone: string;
    notes: string;
  }) => void;
}

export const ClientInformation = ({
  clientInfo,
  setClientInfo,
}: ClientInformationProps) => {
  const handleChange = (field: keyof typeof clientInfo, value: string) => {
    setClientInfo({ ...clientInfo, [field]: value });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <FileText className="h-5 w-5" />
          <span>Client Information</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="clientName">Client Name *</Label>
          <Input
            id="clientName"
            value={clientInfo.name}
            onChange={(e) => handleChange("name", e.target.value)}
            placeholder="Enter client name"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="clientEmail">Client Email *</Label>
          <Input
            id="clientEmail"
            type="email"
            value={clientInfo.email}
            onChange={(e) => handleChange("email", e.target.value)}
            placeholder="Enter client email"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="clientCompany">Company</Label>
          <Input
            id="clientCompany"
            value={clientInfo.company}
            onChange={(e) => handleChange("company", e.target.value)}
            placeholder="Enter company name"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="clientPhone">Phone</Label>
          <Input
            id="clientPhone"
            value={clientInfo.phone}
            onChange={(e) => handleChange("phone", e.target.value)}
            placeholder="Enter phone number"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="clientNotes">Notes</Label>
          <Textarea
            id="clientNotes"
            value={clientInfo.notes}
            onChange={(e) => handleChange("notes", e.target.value)}
            placeholder="Additional notes about the client"
            rows={3}
          />
        </div>
      </CardContent>
    </Card>
  );
};
