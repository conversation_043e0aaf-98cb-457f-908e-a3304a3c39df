import { useState } from "react";
import { Speaker } from "@/types/speaker";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Edit, Upload, User } from "lucide-react";

interface ProposalSpeakerCardProps {
  speaker: Speaker;
  onSelect: (
    speaker: Speaker,
    customRate?: number,
    profileImageUrl?: string,
    customIncluding?: string
  ) => void;
  isSelected?: boolean;
}

const ProposalSpeakerCard = ({
  speaker,
  onSelect,
  isSelected = false,
}: ProposalSpeakerCardProps) => {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [customRate, setCustomRate] = useState(speaker.rate || 0);
  const [profileImageUrl, setProfileImageUrl] = useState<string>("");
  const [customIncluding, setCustomIncluding] = useState<string>("");
  const [isUploading, setIsUploading] = useState(false);

  const handleSelect = () => {
    onSelect(
      speaker,
      customRate,
      profileImageUrl || undefined,
      customIncluding || undefined
    );
    setIsDialogOpen(false);
  };

  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith("image/")) {
      console.error("Invalid file type:", file.type);
      return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      console.error("File too large:", file.size);
      return;
    }

    // Log file details for debugging
    console.log("Processing image file:", {
      name: file.name,
      type: file.type,
      size: file.size,
      lastModified: new Date(file.lastModified).toISOString(),
    });

    setIsUploading(true);
    try {
      // Create a data URL for the image with better quality
      const reader = new FileReader();
      reader.onload = (event) => {
        if (event.target?.result) {
          const dataUrl = event.target.result as string;
          console.log("Image uploaded successfully:", {
            name: file.name,
            type: file.type,
            size: file.size,
            dataUrlLength: dataUrl.length,
            format: dataUrl.split(";")[0].split("/")[1],
          });
          setProfileImageUrl(dataUrl);
        }
      };

      reader.onerror = (error) => {
        console.error("Error reading file:", error);
      };

      // Read as data URL with better quality
      reader.readAsDataURL(file);
    } catch (error) {
      console.error("Error uploading image:", error);
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Card
      className={`transition-all duration-300 hover:shadow-lg h-full ${
        isSelected ? "ring-2 ring-primary" : ""
      }`}
    >
      <CardContent className="p-4">
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-foreground truncate">
              {speaker.name}
            </h3>
            {speaker.rate && speaker.rate > 0 && (
              <div className="text-lg font-bold text-primary">
                ${speaker.rate.toLocaleString()}
              </div>
            )}
          </div>
        </div>

        <div className="mt-4 pt-3 border-t">
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button className="w-full">
                {isSelected ? <Edit className="w-4 h-4 mr-2" /> : null}
                {isSelected ? "Edit Selection" : "Select Speaker"}
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-md">
              <DialogHeader>
                <DialogTitle>Configure Speaker Selection</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div>
                  <Label htmlFor="custom-rate">
                    Custom Rate ($) - Optional
                  </Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="custom-rate"
                      type="number"
                      value={customRate || ""}
                      onChange={(e) =>
                        setCustomRate(
                          e.target.value ? Number(e.target.value) : 0
                        )
                      }
                      placeholder={
                        speaker.rate
                          ? speaker.rate.toString()
                          : "Enter custom rate"
                      }
                      className="flex-1"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => setCustomRate(speaker.rate || 0)}
                      disabled={!speaker.rate}
                      className="whitespace-nowrap text-xs"
                    >
                      Use Standard
                    </Button>
                  </div>
                  {speaker.rate && (
                    <p className="text-xs text-muted-foreground mt-1">
                      Standard rate: ${speaker.rate.toLocaleString()}
                      {customRate && customRate !== speaker.rate && (
                        <span
                          className={`ml-2 font-medium ${
                            customRate > speaker.rate
                              ? "text-green-600"
                              : "text-red-600"
                          }`}
                        >
                          ({customRate > speaker.rate ? "+" : ""}$
                          {(customRate - speaker.rate).toLocaleString()})
                        </span>
                      )}
                    </p>
                  )}
                  <p className="text-xs text-muted-foreground mt-1">
                    Leave empty to use standard rate
                  </p>
                </div>

                <div>
                  <Label htmlFor="profile-image">
                    Upload Speaker Image (Optional)
                  </Label>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-2">
                      <Upload className="w-4 h-4" />
                      <Input
                        id="profile-image"
                        type="file"
                        accept="image/*"
                        onChange={handleImageUpload}
                        disabled={isUploading}
                        className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/80"
                      />
                    </div>

                    <div className="flex items-center justify-center gap-4">
                      {profileImageUrl ? (
                        <div className="text-center">
                          <img
                            src={profileImageUrl}
                            alt="Preview"
                            className="w-20 h-20 rounded-full object-cover border-2 border-primary mx-auto mb-2"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setProfileImageUrl("")}
                            className="text-xs"
                          >
                            Remove Image
                          </Button>
                        </div>
                      ) : speaker.image ? (
                        <div className="text-center">
                          <img
                            src={speaker.image}
                            alt="Current"
                            className="w-20 h-20 rounded-full object-cover border-2 border-muted-foreground mx-auto mb-2"
                          />
                          <p className="text-xs text-muted-foreground">
                            Current speaker image
                          </p>
                        </div>
                      ) : (
                        <div className="text-center">
                          <div className="w-20 h-20 rounded-full bg-muted flex items-center justify-center border-2 border-dashed border-muted-foreground mx-auto mb-2">
                            <User className="w-8 h-8 text-muted-foreground" />
                          </div>
                          <p className="text-xs text-muted-foreground">
                            No image available
                          </p>
                        </div>
                      )}
                    </div>

                    {isUploading && (
                      <div className="flex items-center justify-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                        <p className="text-sm text-muted-foreground">
                          Processing image...
                        </p>
                      </div>
                    )}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Upload a custom image for this speaker in the proposal.
                    Supports JPG, PNG, GIF up to 5MB.
                  </p>
                </div>

                <div>
                  <Label htmlFor="custom-including">
                    Including (e.g., flights, accommodation) - Optional
                  </Label>
                  <Input
                    id="custom-including"
                    value={customIncluding}
                    onChange={(e) => setCustomIncluding(e.target.value)}
                    placeholder="flights, accommodation, local transport"
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    What's included with the speaker fee. Will be displayed as
                    "Fee + including" in the proposal.
                  </p>
                </div>

                <Button
                  onClick={handleSelect}
                  className="w-full"
                  disabled={isUploading}
                >
                  {isSelected ? "Update Selection" : "Select Speaker"}
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>
      </CardContent>
    </Card>
  );
};

export default ProposalSpeakerCard;
