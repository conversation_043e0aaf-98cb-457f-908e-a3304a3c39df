import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { speakersService } from "@/lib/services";
import { Speaker } from "@/types/speaker";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Search, Users, DollarSign, Trash2, ChevronDown } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import ProposalSpeakerCard from "./ProposalSpeakerCard";

interface SpeakerSelectionProps {
  selectedSpeakers: any[];
  setSelectedSpeakers: (speakers: any[]) => void;
  onNext: () => void;
  onPrevious: () => void;
  isLoading?: boolean;
}

const SpeakerSelection = ({
  selectedSpeakers,
  setSelectedSpeakers,
  onNext,
  onPrevious,
  isLoading = false,
}: SpeakerSelectionProps) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [categoryOpen, setCategoryOpen] = useState(false);

  // Fetch speakers from database using Drizzle service
  const { data: speakers = [], isLoading: speakersLoading } = useQuery({
    queryKey: ["speakers"],
    queryFn: async () => {
      try {
        const speakers = await speakersService.getAll();
        return speakers;
      } catch (error) {
        console.error("Error fetching speakers:", error);
        throw error;
      }
    },
  });

  // Get unique categories
  const categories = Array.from(
    new Set(speakers.map((s) => s.category).filter(Boolean))
  );

  // Filter speakers based on search and category
  const filteredSpeakers = speakers.filter((speaker) => {
    const matchesSearch =
      speaker.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      speaker.bio?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory =
      !selectedCategory || speaker.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  const handleSpeakerSelect = (
    speaker: Speaker,
    customRate?: number,
    profileImageUrl?: string
  ) => {
    const speakerData = {
      ...speaker,
      rate: customRate || speaker.rate,
      image: profileImageUrl || speaker.image || undefined,
    };

    const isAlreadySelected = selectedSpeakers.some((s) => s.id === speaker.id);

    if (isAlreadySelected) {
      setSelectedSpeakers(
        selectedSpeakers.map((s) => (s.id === speaker.id ? speakerData : s))
      );
    } else {
      setSelectedSpeakers([...selectedSpeakers, speakerData]);
    }
  };

  const handleRemoveSpeaker = (speakerId: string) => {
    setSelectedSpeakers(selectedSpeakers.filter((s) => s.id !== speakerId));
  };

  const totalCost = selectedSpeakers.reduce(
    (sum, speaker) => sum + (speaker.rate || 0),
    0
  );

  if (speakersLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <Users className="h-12 w-12 animate-pulse text-muted-foreground mx-auto mb-4" />
          <p>Loading speakers...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 max-h-[70vh] overflow-hidden flex flex-col">
      {/* Navigation Buttons - Fixed at top */}
      <div className="flex items-center justify-between py-4 border-b bg-background">
        <Button
          variant="outline"
          onClick={onPrevious}
          className="flex items-center gap-2"
        >
          ← Previous
        </Button>

        <div className="text-sm text-muted-foreground font-medium">
          Step 3 of 3: Select Speakers
        </div>

        <Button
          onClick={onNext}
          disabled={isLoading}
          className="flex items-center gap-2"
        >
          {isLoading ? "Creating..." : "Create Proposal"} →
        </Button>
      </div>

      {/* Selected Speakers Summary */}
      {selectedSpeakers.length > 0 && (
        <Card className="border-2 border-primary/20 bg-primary/5">
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2 text-lg">
              <Users className="h-5 w-5" />
              Selected Speakers ({selectedSpeakers.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="grid gap-2 max-h-32 overflow-y-auto">
              {selectedSpeakers.map((speaker) => (
                <div
                  key={speaker.id}
                  className="flex items-center justify-between p-3 bg-background rounded-lg border"
                >
                  <div className="flex items-center gap-3">
                    {speaker.image && (
                      <img
                        src={speaker.image}
                        alt={speaker.name}
                        className="w-8 h-8 rounded-full object-cover"
                      />
                    )}
                    <div>
                      <h4 className="font-medium text-sm">{speaker.name}</h4>
                      <p className="text-xs text-muted-foreground">
                        {speaker.category}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="font-semibold text-sm">
                      ${(speaker.rate || 0).toLocaleString()}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRemoveSpeaker(speaker.id)}
                      className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-100"
                    >
                      <Trash2 className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
            <div className="pt-2 border-t">
              <div className="flex items-center justify-between text-base font-semibold">
                <span className="flex items-center gap-2">
                  <DollarSign className="h-4 w-4" />
                  Total Cost:
                </span>
                <span className="text-primary text-lg">
                  ${totalCost.toLocaleString()}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Search and Filter Controls */}
      <Card>
        <CardContent className="p-4">
          <div className="flex gap-4 items-end">
            {/* Search Input */}
            <div className="flex-1">
              <label className="text-sm font-medium text-foreground mb-2 block">
                Search Speakers
              </label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                <Input
                  placeholder="Search by name or bio..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Category Dropdown */}
            <div className="min-w-[200px]">
              <label className="text-sm font-medium text-foreground mb-2 block">
                Category
              </label>
              <Popover open={categoryOpen} onOpenChange={setCategoryOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={categoryOpen}
                    className="w-full justify-between"
                  >
                    {selectedCategory || "All Categories"}
                    <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[200px] p-0" align="start">
                  <Command>
                    <CommandInput placeholder="Search categories..." />
                    <CommandList>
                      <CommandEmpty>No categories found.</CommandEmpty>
                      <CommandGroup>
                        <CommandItem
                          onSelect={() => {
                            setSelectedCategory("");
                            setCategoryOpen(false);
                          }}
                        >
                          All Categories
                        </CommandItem>
                        {categories.map((category) => (
                          <CommandItem
                            key={category}
                            onSelect={() => {
                              setSelectedCategory(category);
                              setCategoryOpen(false);
                            }}
                          >
                            {category}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            {/* Clear Filters */}
            {(searchTerm || selectedCategory) && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  setSearchTerm("");
                  setSelectedCategory("");
                }}
                className="whitespace-nowrap"
              >
                Clear Filters
              </Button>
            )}
          </div>

          {/* Active Filters Display */}
          {(searchTerm || selectedCategory) && (
            <div className="flex gap-2 mt-3 pt-3 border-t">
              <span className="text-sm text-muted-foreground">
                Active filters:
              </span>
              {searchTerm && (
                <Badge variant="secondary" className="text-xs">
                  Search: {searchTerm}
                </Badge>
              )}
              {selectedCategory && (
                <Badge variant="secondary" className="text-xs">
                  Category: {selectedCategory}
                </Badge>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Available Speakers - Scrollable */}
      <div className="flex-1 overflow-y-auto">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">
              Available Speakers
              <span className="text-sm font-normal text-muted-foreground ml-2">
                ({filteredSpeakers.length} found)
              </span>
            </h3>
          </div>

          {filteredSpeakers.length === 0 ? (
            <Card>
              <CardContent className="flex flex-col items-center justify-center py-12">
                <Users className="h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-semibold mb-2">
                  No speakers found
                </h3>
                <p className="text-muted-foreground text-center max-w-md">
                  {searchTerm || selectedCategory
                    ? "Try adjusting your search criteria or clearing filters to see more speakers."
                    : "No speakers are available in the database. Please add some speakers first."}
                </p>
                {(searchTerm || selectedCategory) && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSearchTerm("");
                      setSelectedCategory("");
                    }}
                    className="mt-3"
                  >
                    Clear All Filters
                  </Button>
                )}
              </CardContent>
            </Card>
          ) : (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {filteredSpeakers.map((speaker) => {
                const speakerWithImage: Speaker = {
                  ...speaker,
                  availability: speaker.availability as string | null,
                  image: undefined,
                };

                return (
                  <ProposalSpeakerCard
                    key={speaker.id}
                    speaker={speakerWithImage}
                    onSelect={handleSpeakerSelect}
                    isSelected={selectedSpeakers.some(
                      (s) => s.id === speaker.id
                    )}
                  />
                );
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SpeakerSelection;
