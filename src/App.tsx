import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "./components/theme-provider";

import ProtectedRoute from "./components/ProtectedRoute";
import Index from "./pages/Index";
import Login from "./pages/Login";
import SignUp from "./pages/SignUp";
import Speakers from "./pages/Speakers";
import SpeakerDetail from "./pages/SpeakerDetail";
import EditSpeaker from "./pages/EditSpeaker";

import Employees from "./pages/Employees";
import EmployeeDetail from "./pages/EmployeeDetail";

import Profile from "./pages/Profile";
import Proposals from "./pages/Proposals";
import ProposalsList from "./pages/ProposalsList";
import CreateProposal from "./pages/CreateProposal";
import EditProposal from "./pages/EditProposal";

import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

function App() {
  return (
    <QueryClientProvider client={queryClient}>
      <ThemeProvider defaultTheme="dark" storageKey="vite-ui-theme">
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/login" element={<Login />} />
              <Route path="/signup" element={<SignUp />} />
              <Route
                path="/"
                element={
                  <ProtectedRoute>
                    <Index />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/speakers"
                element={
                  <ProtectedRoute>
                    <Speakers />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/speakers/:id"
                element={
                  <ProtectedRoute>
                    <SpeakerDetail />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/speakers/:id/edit"
                element={
                  <ProtectedRoute>
                    <EditSpeaker />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/employees"
                element={
                  <ProtectedRoute>
                    <Employees />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/employees/:id"
                element={
                  <ProtectedRoute>
                    <EmployeeDetail />
                  </ProtectedRoute>
                }
              />

              <Route
                path="/profile"
                element={
                  <ProtectedRoute>
                    <Profile />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/proposals"
                element={
                  <ProtectedRoute>
                    <Proposals />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/proposals-list"
                element={
                  <ProtectedRoute>
                    <ProposalsList />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/create-proposal"
                element={
                  <ProtectedRoute>
                    <CreateProposal />
                  </ProtectedRoute>
                }
              />
              <Route
                path="/proposals/:id/edit"
                element={
                  <ProtectedRoute>
                    <EditProposal />
                  </ProtectedRoute>
                }
              />

              <Route path="*" element={<NotFound />} />
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </ThemeProvider>
    </QueryClientProvider>
  );
}

export default App;
